package com.adins.esign.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.adins.am.model.custom.UpdateableEntity;

/**
 * TrDocumentDRestore generated for document restoration tracking
 */
@Entity
@Table(name = "tr_document_d_restore")
public class TrDocumentDRestore extends UpdateableEntity implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    public static final String ID_DOCUMENT_D_RESTORE_HBM = "idDocumentDRestore";

    private long idDocumentDRestore;
    private TrDocumentD trDocumentD;
    private MsLov lovProcessRestore;
    private Date restoreExpiredDate;
    private String isActive;

    public TrDocumentDRestore() {
    }

    public TrDocumentDRestore(long idDocumentDRestore, TrDocumentD trDocumentD, String isActive,
            String usrCrt, Date dtmCrt) {
        this.idDocumentDRestore = idDocumentDRestore;
        this.trDocumentD = trDocumentD;
        this.isActive = isActive;
        this.usrCrt = usrCrt;
        this.dtmCrt = dtmCrt;
    }

    public TrDocumentDRestore(long idDocumentDRestore, TrDocumentD trDocumentD, MsLov lovProcessRestore,
            Date restoreExpiredDate, String isActive, String usrCrt, Date dtmCrt,
            String usrUpd, Date dtmUpd) {
        this.idDocumentDRestore = idDocumentDRestore;
        this.trDocumentD = trDocumentD;
        this.lovProcessRestore = lovProcessRestore;
        this.restoreExpiredDate = restoreExpiredDate;
        this.isActive = isActive;
        this.usrCrt = usrCrt;
        this.dtmCrt = dtmCrt;
        this.usrUpd = usrUpd;
        this.dtmUpd = dtmUpd;
    }

    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_document_d_restore", unique = true, nullable = false)
    public long getIdDocumentDRestore() {
        return this.idDocumentDRestore;
    }

    public void setIdDocumentDRestore(long idDocumentDRestore) {
        this.idDocumentDRestore = idDocumentDRestore;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_document_d", nullable = false)
    public TrDocumentD getTrDocumentD() {
        return this.trDocumentD;
    }

    public void setTrDocumentD(TrDocumentD trDocumentD) {
        this.trDocumentD = trDocumentD;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lov_process_restore")
    public MsLov getLovProcessRestore() {
        return this.lovProcessRestore;
    }

    public void setLovProcessRestore(MsLov lovProcessRestore) {
        this.lovProcessRestore = lovProcessRestore;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "restore_expired_date")
    public Date getRestoreExpiredDate() {
        return this.restoreExpiredDate;
    }

    public void setRestoreExpiredDate(Date restoreExpiredDate) {
        this.restoreExpiredDate = restoreExpiredDate;
    }

    @Column(name = "is_active", nullable = false, length = 1)
    public String getIsActive() {
        return this.isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }
}