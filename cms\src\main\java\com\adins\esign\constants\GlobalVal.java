package com.adins.esign.constants;

import com.adins.esign.constants.enums.GeneralSettingType;

public class GlobalVal {
	/**
	 * The class shall not be instantiated.
	 * This constructor will throw IllegalStateException when instantiated
	 */
	protected GlobalVal() {
		throw new IllegalStateException(
				"GlobalVal class shall not be instantiated! Class=" + this.getClass().getName());
	}

	/**
	 * Dispatch task value, used in paging.
	 */
	public static final String DISPATCH_FIRST = "First";
	public static final String DISPATCH_GO = "Go";
	public static final String DISPATCH_LAST = "Last";
	public static final String DISPATCH_NEXT = "Next";
	public static final String DISPATCH_SORT = "Sort";
	public static final String DISPATCH_PREVIOUS = "Prev";

	/**
	 * Constant for searching (number of data column per page).
	 */
	public static final int COL_PER_PAGE = 20;

	/**
	 * Constant for paging (number of form per page).
	 */
	public static final int FORM_PER_PAGE = 10;

	/**
	 * Constant for paging (start row).
	 */
	public static final int START_ROW = 1;

	/**
	 * Constant for stacking (number of stack for storing page parameter).
	 */
	public static final int STACK_PER_PAGE = 5;

	/**
	 * Constant for paging (number of data row per page).
	 */
	public static final int ROW_PER_PAGE = 25;
	public static final int ROW_PER_PAGE_LOOKUP = 10;
	public static final int ROW_PER_PAGE_UNASSIGN = 100;

	/**
	 * Constant for row ordering (ascending).
	 */
	public static final String ROW_ORDER_ASC = "ASC";

	/**
	 * Constant for row ordering (descending).
	 */
	public static final String ROW_ORDER_DESC = "DESC";

	/**
	 * String that is used for login identifier for all process done by SYSTEM.
	 */
	public static final String SYSTEM_LOGINID = "SYSTEM";
	public static final String DIGI_LOGINID = "DIGI";
	public static final String SCHEDULER = "SCHEDULER";
	public static final String SCHEDULER_WORKFLOW = "SCHEDULER_WORKFLOW";

	public static final String FILENAME_REPORT_BY_USER = "ByUser";
	public static final String FILENAME_REPORT_BY_BRANCH = "ByBranch";
	public static final String REPORT_BY_USER = "By User";
	public static final String REPORT_BY_BRANCH = "By Branch";

	// flag source SPVADHOC
	public static final String NC = "NC";

	public static final String SUBSYSTEM_ESIGN = "ESIGN";

	public static final String ROLE_ADMIN_ESIGN = "ADMESIGN";
	public static final String ROLE_ADMIN_CLIENT = "ADMCLIENT";
	public static final String ROLE_ADMIN_LEGAL = "ADMLEGAL";
	public static final String ROLE_ADMIN_CREDIT = "ADMCREDIT";
	public static final String ROLE_CUSTOMER = "CUST";
	public static final String ROLE_BM_MF = "MF";

	public static final String ROLE_NAME_ADMIN_CLIENT = "Admin Client";

	public static final String EXTENTION_PICTURE_JPEG = ".jpeg";

	public static final String FILE_EXTENTION_XLSX = ".xlsx";

	/*
	 * MENU CODE
	 */
	public static final String MENU_BALANCE = "BALANCE";
	public static final String MENU_STAMP_DUTY = "STAMP_DUTY";
	public static final String MENU_INQ_USER = "INQUIRY_USER";
	public static final String MENU_FEEDBACK = "FEEDBACK";
	public static final String MENU_EMPLOYEE = "EMPLOYEE";
	public static final String MENU_CUSTOMER = "CUSTOMER";
	public static final String MENU_TENANT_SETTING = "TENANT_SETTING";

	public static final String OFFICE_HO = "HEAD_OFFICE";
	public static final String OFFICE_CODE_HO = "HO";

	public static final String INQUIRY_TYPE_INBOX = "INBOX";
	public static final String INQUIRY_TYPE_LIST = "LIST";
	public static final String INQUIRY_TYPE_LIST_CUST = "LIST_CUST";
	public static final String INQUIRY_TYPE_LIST_NON_CUST = "LIST_NON_CUST";
	public static final String INQUIRY_TYPE_LIST_BM_MF = "LIST_BM_MF";

	public static final String INQUIRY_USER_REQUEST_TYPE_CUSTOMER = "CUSTOMER";
	public static final String INQUIRY_USER_REQUEST_TYPE_EMPLOYEE = "KARYAWAN";

	public static final String LOOKUP_TYPE_BRANCH_HIRARKI_USER = "EXCU";
	public static final String LOOKUP_TYPE_BRANCH_DEALER_USER = "DOBU";
	public static final String LOOKUP_TYPE_BRANCH_HIRARKI = "EXC";
	public static final String LOOKUP_TYPE_BRANCH_DEALER = "DOB";

	public static final String LOOKUP_TYPE_JOB_HIRARKI = "EXC";
	public static final String LOOKUP_TYPE_JOB_ADMIN_USER = "UMGE";
	public static final String LOOKUP_TYPE_PARENT_ABLE = "PAR";

	public static final String LOOKUP_TYPE_USER_MANAGER_BRANCH = "BRH";
	public static final String LOOKUP_TYPE_USER_MANAGER_DEALER = "DLR";

	public static final String FLAG_LOGIN_PROVIDER_DB = "DB";
	public static final String FLAG_LOGIN_PROVIDER_AD = "AD";
	public static final String FLAG_LOGIN_PROVIDER_NC = "NC";

	public static final String SERVICES_RESULT_SUCCESS = "Success";

	public static final String BALANCE_MUTATION_STATUS_SUCCESS = "SUCCESS";
	public static final String BALANCE_MUTATION_STATUS_FAILED = "FAILED";

	public static final String ANSWER_TYPE_TEXT = "001";
	public static final String ANSWER_TYPE_TEXT_MULTILINE = "002";
	public static final String ANSWER_TYPE_CURRENCY = "003";
	public static final String ANSWER_TYPE_NUMERIC = "004";
	public static final String ANSWER_TYPE_DECIMAL = "005";
	public static final String ANSWER_TYPE_MULTIPLE = "006";
	public static final String ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION = "007";
	public static final String ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION = "008";
	public static final String ANSWER_TYPE_RADIO = "009";
	public static final String ANSWER_TYPE_RADIO_WITH_DESCRIPTION = "010";
	public static final String ANSWER_TYPE_DROPDOWN = "011";
	public static final String ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION = "012";
	public static final String ANSWER_TYPE_DATE = "013";
	public static final String ANSWER_TYPE_TIME = "014";
	public static final String ANSWER_TYPE_DATETIME = "015";
	public static final String ANSWER_TYPE_IMAGE = "016";
	public static final String ANSWER_TYPE_IMAGE_WITH_GEODATA = "017";
	public static final String ANSWER_TYPE_IMAGE_WITH_GPS = "018";
	public static final String ANSWER_TYPE_LOOKUP = "019";
	public static final String ANSWER_TYPE_LOOKUP_WITH_FILTER = "020";
	public static final String ANSWER_TYPE_DRAWING = "021";
	public static final String ANSWER_TYPE_RESPONDEN = "022";
	public static final String ANSWER_TYPE_DATA_LIST = "023";
	public static final String ANSWER_TYPE_LOCATION = "024";
	public static final String ANSWER_TYPE_TEXT_WITH_SUGGESTION = "025";
	public static final String ANSWER_TYPE_LU_ONLINE = "026";
	public static final String ANSWER_TYPE_PHOTO_KTP = "027";

	public static final String ACTION_CANCEL = "CANCEL";

	/*
	 * General Setting DataType
	 */
	public static final String GENERAL_SETTING_TYPE_BOOLEAN = GeneralSettingType.BOOLEAN.toString();
	public static final String GENERAL_SETTING_TYPE_INTEGER = GeneralSettingType.INTEGER.toString();
	public static final String GENERAL_SETTING_TYPE_STRING = GeneralSettingType.STRING.toString();
	public static final String GENERAL_SETTING_TYPE_URL = GeneralSettingType.URL.toString();

	/**
	 * Token separator, used in loginId to identify tenant in multitenant
	 * configuration
	 */
	public static final char TENANT_TOKEN = '@';

	/*
	 * LOV Group
	 */
	public static final String LOV_GROUP_PROVINSI = "PROVINSI";
	public static final String LOV_GROUP_KOTA = "KOTA";
	public static final String LOV_GROUP_KECAMATAN = "KECAMATAN";
	public static final String LOV_GROUP_KELURAHAN = "KELURAHAN";
	public static final String LOV_GROUP_KODE_POS = "KODE POS";
	public static final String LOV_GROUP_SIGN_TYPE = "SIGN_TYPE";
	public static final String LOV_GROUP_SIGNER_TYPE = "SIGNER_TYPE";
	public static final String LOV_GROUP_PWD_CHANGE_TYPE = "PWD_CHANGE_TYPE";
	public static final String LOV_GROUP_DOC_TYPE = "DOC_TYPE";
	public static final String LOV_GROUP_BALANCE_TYPE = "BALANCE_TYPE";
	public static final String LOV_GROUP_TRX_TYPE = "TRX_TYPE";
	public static final String LOV_GROUP_SDT_STATUS = "STAMP_DUTY_STATUS";
	public static final String LOV_GROUP_SIGN_STATUS = "LOV_SIGN_STATUS";
	public static final String LOV_GROUP_PAY_SIGN_TYPE = "PAYMENT_SIGN_TYPE";
	public static final String LOV_GROUP_VENDOR_TYPE = "VENDOR_TYPE";
	public static final String LOV_GROUP_JOB_TYPE = "JOB_TYPE";
	public static final String LOV_GROUP_SCHEDULER_TYPE = "SCHEDULER_TYPE";
	public static final String LOV_GROUP_ERR_HIST_MODULE = "ERR_HIST_MODULE";
	public static final String LOV_GROUP_ID_TYPE = "ID_TYPE";
	public static final String LOV_GROUP_VENDOR_SIGN_PAYMENT_TYPE = "VENDOR_SIGN_PAYMENT_TYPE";
	public static final String LOV_GROUP_AUTOSIGN = "AUTOSIGN";
	public static final String LOV_GROUP_USER_TYPE = "USER_TYPE";
	public static final String LOV_GROUP_MESSAGE_MEDIA = "MESSAGE_MEDIA";
	public static final String LOV_GROUP_CALLBACK_TYPE = "CALLBACK_TYPE";
	public static final String LOV_GROUP_SMS_GATEWAY = "SMS_GATEWAY";
	public static final String LOV_GROUP_VENDOR_STAMPING = "LOV_VENDOR_STAMPING";
	public static final String LOV_GROUP_LOG_ACTION_TYPE = "LOG_ACTION_TYPE";
	public static final String LOV_GROUP_ACTIVITY_LOG_TYPE = "ACTIVITY_LOG_TYPE";
	public static final String LOV_GROUP_TENANT_SETTING_TYPE = "TENANT_SETTING_TYPE";
	public static final String LOV_GROUP_WA_GATEWAY = "WA_GATEWAY";
	public static final String LOV_GROUP_SIGNING_PROCESS_TYPE = "SIGNING_PROCESS_TYPE";
	public static final String LOV_GROUP_NOTIF_SENDING_POINT = "NOTIF_SENDING_POINT";
	public static final String LOV_GROUP_DEFAULT_SENDING_OPTION = "DEFAULT_SENDING_OPTION";
	public static final String LOV_GROUP_REPORT_TYPE = "REPORT_TYPE";
	public static final String LOV_GROUP_PROCESS_RESTORE = "PROCESS_RESTORE";
	public static final String LOV_GROUP_CREDENTIAL_TYPE = "CREDENTIAL_TYPE";

	public static final String CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_NORMAL = "OTP_SIGN_NORMAL";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EMBED_V2 = "OTP_SIGN_EMBED_V2";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_OTP_ACTIVATION = "OTP_ACT";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_RESET_PASSW0RD = "RESET_PASSWORD";
	public static final String CODE_LOV_RESEND_SIGN_NOTIF = "RESEND_SIGN_NOTIF";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_OTP_SIGN_EXTERNAL = "OTP_SIGN_EXTERNAL";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_OTP_EMAIL_INVITATION = "OTP_EMAIL_INVITATION";
	public static final String CODE_LOV_RESEND_SIGN_NOTIF_EMBED_V1 = "RESEND_SIGN_NOTIF_EMBED_V1";
	public static final String CODE_LOV_RESEND_SIGN_NOTIF_EMBED_V2 = "RESEND_SIGN_NOTIF_EMBED_V2";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_GENERATE_INVITATION = "GEN_INV";
	public static final String CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT = "SEND_DOC";
	public static final String CODE_LOV_PROCESS_DOWNLOAD_DOCUMENT = "DOWNLOAD_DOCUMENT";
	public static final String CODE_LOV_PROCESS_VIEW_DOCUMENT = "VIEW_DOCUMENT";
	/*
	 * LOV Constraint
	 */
	public static final String LOV_CONSTRAINT_CONFIG_BALANCE = "CONFIG_BALANCE";

	/*
	 * Code LOV
	 */
	public static final String CODE_LOV_MALE = "M";
	public static final String CODE_LOV_FEMALE = "F";

	public static final String PAY_SIGN_TYPE_DOC = "DOC";
	public static final String PAY_SIGN_TYPE_SIGN = "TTD";

	public static final String CODE_LOV_SIGN_TYPE_PRF = "PRF";
	public static final String CODE_LOV_SIGN_TYPE_TTD = "TTD";
	public static final String CODE_LOV_SIGN_TYPE_SDT = "SDT";

	public static final String CODE_LOV_SIGNER_TYPE_CUST = "CUST";
	public static final String CODE_LOV_SIGNER_TYPE_GRT = "GRT";
	public static final String CODE_LOV_SIGNER_TYPE_SUPP = "SUPP";
	public static final String CODE_LOV_SIGNER_TYPE_MF = "MF";
	public static final String CODE_LOV_SIGNER_TYPE_SPS = "SPS";

	public static final String CODE_LOV_AUTOSIGN = "AUTO";
	public static final String CODE_LOV_MANUALSIGN = "MANUAL";

	public static final String CODE_LOV_BALANCE_TYPE_SDT = "SDT";
	public static final String CODE_LOV_BALANCE_TYPE_VRF = "VRF";
	public static final String CODE_LOV_BALANCE_TYPE_VRF_V2 = "VRF_V2";
	public static final String CODE_LOV_BALANCE_TYPE_OTP = "OTP";
	public static final String CODE_LOV_BALANCE_TYPE_DOC = "DOC";
	public static final String CODE_LOV_BALANCE_TYPE_SGN = "SGN";
	public static final String CODE_LOV_BALANCE_TYPE_SMS = "SMS";
	public static final String CODE_LOV_BALANCE_TYPE_FVRF = "FVRF";
	public static final String CODE_LOV_BALANCE_TYPE_SDT_POSTPAID = "SDT_POSTPAID";
	public static final String CODE_LOV_BALANCE_TYPE_LIVENESS_FACECOMPARE = "LIVENESS_FACECOMPARE";
	public static final String CODE_LOV_BALANCE_TYPE_LIVENESS = "LIVENESS";
	public static final String CODE_LOV_BALANCE_TYPE_FACECOMPARE = "FACECOMPARE";
	public static final String CODE_LOV_BALANCE_TYPE_PNBP = "PNBP";
	public static final String CODE_LOV_BALANCE_TYPE_WA = "WA";
	public static final String CODE_LOV_BALANCE_TYPE_WA_OTP = "WA_OTP";
	public static final String CODE_LOV_BALANCE_TYPE_TEXT_VRF = "TEXT_VRF";
	public static final String CODE_LOV_BALANCE_TYPE_RESTORE_DOCUMENT = "RESTORE_DOCUMENT";

	public static final String CODE_LOV_SDT_AVAILABLE = "AVAILABLE";
	public static final String CODE_LOV_SDT_USED = "USED";
	public static final String CODE_LOV_SDT_STAMP_FAILED = "STAMP_FAILED";
	public static final String CODE_LOV_SDT_GO_LIVE = "GO LIVE";

	public static final String CODE_LOV_SIGN_STATUS_COMPLETED = "CP";
	public static final String CODE_LOV_SIGN_STATUS_NEED_SIGN = "NS";

	public static final String CODE_LOV_TRX_TYPE_CNCL = "CNCL";

	public static final String CODE_LOV_TRX_TYPE_USDT = "USDT";
	public static final String CODE_LOV_TRX_TYPE_TSDT = "TSDT";

	public static final String CODE_LOV_TRX_TYPE_UOTP = "UOTP";
	public static final String CODE_LOV_TRX_TYPE_TOTP = "TOTP";

	public static final String CODE_LOV_TRX_TYPE_UDOC = "UDOC";
	public static final String CODE_LOV_TRX_TYPE_TDOC = "TDOC";

	public static final String CODE_LOV_TRX_TYPE_USGN = "USGN";
	public static final String CODE_LOV_TRX_TYPE_TSGN = "TSGN";

	public static final String CODE_LOV_TRX_TYPE_USMS = "USMS";
	public static final String CODE_LOV_TRX_TYPE_TSMS = "TSMS";

	public static final String CODE_LOV_TRX_TYPE_UFVRF = "UFVRF";
	public static final String CODE_LOV_TRX_TYPE_TFVRF = "TFVRF";

	public static final String CODE_LOV_TRX_TYPE_UVRF = "UVRF";
	public static final String CODE_LOV_TRX_TYPE_TVRF = "TVRF";

	public static final String CODE_LOV_TRX_TYPE_UVRF_V2 = "UVRF_V2";
	public static final String CODE_LOV_TRX_TYPE_TVRF_V2 = "TVRF_V2";

	public static final String CODE_LOV_TRX_TYPE_USDT_POSTPAID = "USDT_POSTPAID";
	public static final String CODE_LOV_TRX_TYPE_TSDT_POSTPAID = "TSDT_POSTPAID";

	public static final String CODE_LOV_TRX_TYPE_UPNBP = "UPNBP";
	public static final String CODE_LOV_TRX_TYPE_TPNBP = "TPNBP";

	public static final String CODE_LOV_TRX_TYPE_UWA = "UWA";
	public static final String CODE_LOV_TRX_TYPE_TWA = "TWA";

	public static final String CODE_LOV_TRX_TYPE_UWA_OTP = "UWA_OTP";
	public static final String CODE_LOV_TRX_TYPE_TWA_OTP = "TWA_OTP";

	public static final String CODE_LOV_TRX_TYPE_UTEXT_VRF = "UTEXT_VRF";
	public static final String CODE_LOV_TRX_TYPE_TTEXT_VRF = "TTEXT_VRF";

	public static final String CODE_LOV_TRX_TYPE_ULIVENESS_FACECOMPARE = "ULIVENESS_FACECOMPARE";
	public static final String CODE_LOV_TRX_TYPE_ULIVENESS = "ULIVENESS";
	public static final String CODE_LOV_TRX_TYPE_UFACECOMPARE = "UFACECOMPARE";

	public static final String CODE_LOV_TRX_TYPE_USE_RESTORE_DOCUMENT = "USE_RESTORE_DOCUMENT";

	public static final String CODE_LOV_VENDOR_TYPE_PSRE = "PSRE";
	public static final String CODE_LOV_VENDOR_TYPE_HOSTING = "HOSTING";
	public static final String CODE_LOV_VENDOR_TYPE_E_MATERAI = "E-MATERAI";
	public static final String CODE_LOV_VENDOR_TYPE_MESSAGE_GATEWAY = "MG";

	public static final String CODE_LOV_PWD_CHANGE_TYPE_NEW = "NEW";
	public static final String CODE_LOV_PWD_CHANGE_TYPE_RESET = "RESET";
	public static final String CODE_LOV_PWD_CHANGE_TYPE_CHANGE = "CHANGE";

	public static final String CODE_LOV_NOTIF_TYPE_SMS = "SMS";
	public static final String CODE_LOV_NOTIF_TYPE_MIX = "MIX";

	public static final String CODE_LOV_JOB_TYPE_BALREM = "BALREM";
	public static final String CODE_LOV_JOB_TYPE_DEMAIL = "DEMAIL";
	public static final String CODE_LOV_JOB_TYPE_REMAIL = "REMAIL";
	public static final String CODE_LOV_JOB_TYPE_SYNCBAL = "SYNCBAL";
	public static final String CODE_LOV_JOB_TYPE_BALRECAP = "BALRECAP";
	public static final String CODE_LOV_JOB_TYPE_SYNCCERTEXPDATE = "SYNCCERTEXPDATE";
	public static final String CODE_LOV_JOB_TYPE_SYNCREGTEKEN = "SYNCREGTEKEN";
	public static final String CODE_LOV_JOB_TYPE_ERRHISTRERUN = "ERRHISTRERUN";
	public static final String CODE_LOV_JOB_TYPE_REKONSIL = "REKONSIL";
	public static final String CODE_LOV_JOB_DEL_RESULTSTAMP_ONPREM = "DEL_RESULTSTAMP_ONPREM";

	public static final String CODE_LOV_TIPE_IDENTITAS_NIK = "KTP";
	public static final String CODE_LOV_TIPE_IDENTITAS_NPWP = "NPWP";

	public static final String CODE_LOV_SCHED_TYPE_DAILY = "DAILY";
	public static final String CODE_LOV_SCHED_TYPE_CONTINOUS = "CONT";
	public static final String CODE_LOV_SCHED_TYPE_NON_SCHED = "NON";

	public static final String CODE_LOV_ERR_HIST_MODULE_GEN_INV = "GEN_INV";
	public static final String CODE_LOV_ERR_HIST_MODULE_SEND_DOC = "SEND_DOC";
	public static final String CODE_LOV_ERR_HIST_MODULE_SIGN_DOC = "SIGN_DOC";

	public static final String CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_MIX = "MIX";
	public static final String CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_SIGN_ONLY = "SIGN_ONLY";
	public static final String CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_DOC_ONLY = "DOC_ONLY";

	public static final String CODE_LOV_USER_TYPE_CUST = "CUST";
	public static final String CODE_LOV_USER_TYPE_EMPLOYEE = "EMPLOYEE";

	// LOV Callback Type
	public static final String CODE_LOV_CALLBACK_TYPE_ACTIVATION_COMPLETE = "ACTIVATION_COMPLETE";
	public static final String CODE_LOV_CALLBACK_TYPE_SIGNING_COMPLETE = "SIGNING_COMPLETE";
	public static final String CODE_LOV_CALLBACK_TYPE_DOCUMENT_SIGN_COMPLETE = "DOCUMENT_SIGN_COMPLETE";
	public static final String CODE_LOV_CALLBACK_TYPE_ALL_DOCUMENT_SIGN_COMPLETE = "ALL_DOCUMENT_SIGN_COMPLETE";

	public static final String CODE_LOV_SMS_GATEWAY_PRIVY = "PRIVY";
	public static final String CODE_LOV_SMS_GATEWAY_JATIS = "JATIS";
	public static final String CODE_LOV_SMS_GATEWAY_VFIRST = "VFIRST";
	public static final String CODE_LOV_SMS_GATEWAY_WHATSAPP = "WHATSAPP";
	public static final String CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS = "WHATSAPP_HALOSIS";

	// LOV Credential Type
	public static final String CODE_LOV_CREDENTIAL_TYPE_TENANT = "TENANT";
	public static final String CODE_LOV_CREDENTIAL_TYPE_ESIGN = "ESIGN";

	public static final String CODE_LOV_LOG_ACTION_TYPE_VIEW_OTP = "VIEW_OTP";
	public static final String CODE_LOV_LOG_ACTION_TYPE_VIEW_RESET_CODE = "VIEW_RESET_CODE";
	public static final String CODE_LOV_LOG_ACTION_TYPE_EDIT_SIGNER_DATA = "EDIT_SIGNER_DATA";
	public static final String CODE_LOV_LOG_ACTION_TYPE_EDIT_ACT_STATUS = "EDIT_ACT_STATUS";
	public static final String CODE_LOV_LOG_ACTION_TYPE_UPDATE_INV_LINK = "UPDATE_INV_LINK";
	public static final String CODE_LOV_LOG_ACTION_TYPE_REGEN_INV_LINK = "REGEN_INV_LINK";
	public static final String CODE_LOV_LOG_ACTION_TYPE_GEN_INV_LINK = "GEN_INV_LINK";

	public static final String CODE_LOV_ACTIVITY_LOG_TYPE_LOGIN = "LOGIN";
	public static final String CODE_LOV_ACTIVITY_LOG_TYPE_LOGOUT = "LOGOUT";

	public static final String CODE_LOV_MESSAGE_MEDIA_SMS = "SMS";
	public static final String CODE_LOV_MESSAGE_MEDIA_WA = "WA";
	public static final String CODE_LOV_MESSAGE_MEDIA_EMAIL = "EMAIL";

	// Tenant settings
	public static final String CODE_LOV_TENANT_SETTING_ACTIVATION_CALLBACK = "ACTIVATION_CALLBACK";
	public static final String CODE_LOV_TENANT_SETTING_SIGNER_COMPLETE_CALLBACK = "SIGNER_COMPLETE_CALLBACK";
	public static final String CODE_LOV_TENANT_SETTING_DOCUMENT_COMPLETE_CALLBACK = "DOCUMENT_COMPLETE_CALLBACK";
	public static final String CODE_LOV_TENANT_SETTING_ALL_DOCUMENT_COMPLETE_CALLBACK = "ALL_DOCUMENT_COMPLETE_CALLBACK";
	public static final String CODE_LOV_TENANT_SETTING_SMS_JATIS_ID = "SMS_JATIS_ID";
	public static final String CODE_LOV_TENANT_SETTING_SMS_JATIS_PASSWORD = "SMS_JATIS_PASSWORD";
	public static final String CODE_LOV_TENANT_SETTING_SMS_JATIS_SENDER = "SMS_JATIS_SENDER";
	public static final String CODE_LOV_TENANT_SETTING_SMS_JATIS_DIVISION = "SMS_JATIS_DIVISION";
	public static final String CODE_LOV_TENANT_SETTING_ALLOW_OTP_SIGN_BY_EMAIL = "ALLOW_OTP_SIGN_BY_EMAIL";
	public static final String CODE_LOV_TENANT_SETTING_MUST_SINGLE_SIGN_SAME_PRIORITY_SEQUENCE = "MUST_SINGLE_SIGN_SAME_PRIORITY_SEQUENCE";
	public static final String CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_SIGNING = "ALLOW_NO_PASSWORD_FOR_SIGNING";
	public static final String CODE_LOV_TENANT_SETTING_ALLOW_NO_PASSWORD_FOR_ACTIVATION = "ALLOW_NO_PASSWORD_FOR_ACTIVATION";
	public static final String CODE_LOV_TENANT_SETTING_OTP_RESET_PWD_DAILY = "OTP_RESET_PWD_DAILY";
	public static final String CODE_LOV_TENANT_SETTING_OTP_ACTIVATION_MAX_ATTEMPTS = "OTP_ACTIVATION_MAX_ATTEMPTS";
	public static final String CODE_LOV_TENANT_SETTING_OTP_SIGN_MAX_ATTEMPTS = "OTP_SIGN_MAX_ATTEMPTS";
	public static final String CODE_LOV_TENANT_SETTING_OTP_SIGN_EMBED_MAX_ATTEMPTS = "OTP_SIGN_EMBED_MAX_ATTEMPTS";
	public static final String CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT = "LIVENESS_FACECOMPARE_VALIDATION_USER_DAILY_LIMIT";
	public static final String CODE_LOV_TENANT_SETTING_LIVENESS_FACECOMPARE_USER_DAILY_LIMIT = "LIVENESS_FACECOMPARE_USER_DAILY_LIMIT";
	public static final String CODE_LOV_TENANT_SETTING_REDIRECT_AFTER_SIGNING = "REDIRECT_AFTER_SIGNING";
	public static final String CODE_LOV_TENANT_SETTING_TIME_TO_REDIRECT = "TIME_TO_REDIRECT";
	public static final String CODE_LOV_TENANT_SETTING_REDIRECT_URL_AFTER_ACTIVATION = "REDIRECT_URL_AFTER_ACTIVATION";
	public static final String CODE_LOV_TENANT_SETTING_DURATION_RESEND_OTP_RESET_PASS = "DURATION_RESEND_OTP_RESET_PASS";
	public static final String CODE_LOV_TENANT_SETTING_DURATION_DURATION_RESEND_OTP_SIGNING = "DURATION_RESEND_OTP_SIGNING";
	public static final String CODE_LOV_TENANT_SETTING_DURATION_DURATION_RESEND_OTP_EMAIL_ACTIVATION = "DURATION_RESENT_OTP_REGISTRATION";
	public static final String CODE_LOV_TENANT_SETTING_MUST_SCROLL_TO_SIGN = "MUST_SCROLL_TO_SIGN";
	public static final String CODE_LOV_TENANT_SETTING_CLIENT_SECRET_POA_VIDA = "CLIENT_SECRET_POA_VIDA";
	public static final String CODE_LOV_TENANT_SETTING_CLIENT_ID_POA_VIDA = "CLIENT_ID_POA_VIDA";
	public static final String CODE_LOV_TENANT_SETTING_ATTACH_SDT_ERROR_EMAIL_RECEIVER = "ATTACH_SDT_ERROR_EMAIL_RECEIVER";
	public static final String CODE_LOV_TENANT_SETTING_PERURI_ACCOUNT_NAME = "PERURI_ACCOUNT_NAME";
	public static final String CODE_LOV_TENANT_SETTING_PERURI_ACCOUNT_PASSWORD = "PERURI_ACCOUNT_PASSWORD";
	public static final String CODE_LOV_TENANT_SETTING_TOKEN_CLIENT_URL_UPLOAD = "TOKEN_CLIENT_URL_UPLOAD";
	public static final String LOV_CODE_TENANT_SETTING_DAYS_ADD_TO_ENCRYPT_DOCUMENT_TENANT = "DAYS_ADD_TO_ENCRYPT_DOCUMENT_TENANT";
	public static final String LOV_CODE_TENANT_SETTING_USE_STAMPING_SYNC_URL = "USE_STAMPING_SYNC_URL";
	public static final String LOV_CODE_TENANT_SETTING_DMS_USERNAME = "DMS_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_USE_PNBP_REGISTER_VIDA = "USE_PNBP_REGISTER_VIDA";
	public static final String LOV_CODE_TENANT_SETTING_POA_CUSTOM_SIGN_STATUS = "POA_CUSTOM_SIGN_STATUS";
	public static final String LOV_CODE_TENANT_SETTING_USE_SYNC_PRIVY_VERIF = "USE_SYNC_PRIVY_VERIF";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_APPLICATION_ID = "PRIVY_LIVENESS_V3_APPLICATION_ID";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_CLIENT_ID = "PRIVY_LIVENESS_V3_CLIENT_ID";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_CLIENT_SECRET = "PRIVY_LIVENESS_V3_CLIENT_SECRET";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_SALT = "PRIVY_LIVENESS_V3_SALT";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_LIVENESS_V3_PUBLIC_KEY = "PRIVY_LIVENESS_V3_PUBLIC_KEY";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_USERNAME = "PRIVY_SYNC_VERIF_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_PASSWORD = "PRIVY_SYNC_VERIF_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_PRIVY_SYNC_VERIF_MERCHANT_KEY = "PRIVY_SYNC_VERIF_MERCHANT_KEY";
	public static final String LOV_CODE_TENANT_SETTING_DOCUMENT_RESTORED_DURATION = "DOCUMENT_RESTORED_DURATION";
	public static final String LOV_CODE_TENANT_SETTING_LIMIT_SEND_NOTIF_BY_PERIOD = "LIMIT_SEND_NOTIF_BY_PERIOD";
	public static final String LOV_CODE_TENANT_SETTING_LIMIT_NOTIF_TIME_PERIOD = "LIMIT_NOTIF_TIME_PERIOD";
	public static final String LOV_CODE_TENANT_SETTING_SMS_VFIRST_USERNAME = "SMS_VFIRST_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_SMS_VFIRST_PASSWORD = "SMS_VFIRST_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_SMS_VFIRST_SENDER = "SMS_VFIRST_SENDER";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_USERNAME = "SMS_JATIS_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_PASSWORD = "SMS_JATIS_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_SMS_JATIS_SENDER = "SMS_JATIS_SENDER";
	public static final String LOV_CODE_TENANT_SETTING_WA_JATIS_USERNAME = "WA_JATIS_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_WA_JATIS_PASSWORD = "WA_JATIS_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_WA_JATIS_SENDER = "WA_JATIS_SENDER";
	public static final String LOV_CODE_TENANT_SETTING_WA_HALOSIS_USERNAME = "WA_HALOSIS_USERNAME";
	public static final String LOV_CODE_TENANT_SETTING_WA_HALOSIS_PASSWORD = "WA_HALOSIS_PASSWORD";
	public static final String LOV_CODE_TENANT_SETTING_WA_HALOSIS_SENDER = "WA_HALOSIS_SENDER";

	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_REQUEST_OTP = "REQUEST_OTP";
	public static final String CODE_LOV_REQUEST_SIGN = "SIGN_REQUEST";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_LIVENESS_FACECOMPARE = "VERIFY_LIVENESS_FACECOMPARE";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_INV_LINK_REQ = "INV_LINK_REQ";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_REGEN_INV = "REGEN_INV";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_RESEND_INV = "RESEND_INV";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_ECERT_NOTIF = "ECERT_NOTIF";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_VERIFY_OTP = "VERIFY_OTP";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST = "SIGN_REQUEST";
	public static final String CODE_LOV_SIGNING_REQUESTED = "SIGNING_REQUESTED";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_REGISTRATION = "REGISTRATION";
	public static final String CODE_LOV_SIGNING_PROCESS_TYPE_UPDATE_SIGNER_DATA = "UPDATE_SIGNER_DATA";

	/*
	 * Code DIGISIGN
	 */
	public static final String CODE_DIGISIGN_MALE = "Laki-laki";
	public static final String CODE_DIGISIGN_FEMALE = "Perempuan";
	public static final String CODE_DIGISIGN_AUTOSIGN = "at";
	public static final String CODE_DIGISIGN_MANUALSIGN = "mt";
	public static final String CODE_DIGISIGN_PAY_PER_DOC = "3";
	public static final String CODE_DIGISIGN_PAY_PER_SIGN = "2";

	/*
	 * IMAGE PREFIX
	 */
	public static final String IMG_PNG_PREFIX = "data:image/png;base64,";
	public static final String IMG_JPEG_PREFIX = "data:image/jpeg;base64,";
	public static final String IMG_JPG_PREFIX = "data:image/jpg;base64,";

	/*
	 * FILE FORMAT
	 */
	public static final String FILE_FORMAT_JPEG = ".jpeg";
	public static final String FILE_FORMAT_JPG = ".jpg";
	public static final String FILE_FORMAT_PNG = ".png";
	public static final String FILE_FORMAT_PDF = ".pdf";
	public static final String FILE_DIGI_FORMAT_JPEG = ".jpeg\"";

	/*
	 * DATE FORMAT
	 */
	public static final String DATE_TIME_FORMAT_SEQ = "yyyyMMddHHmmsss";
	public static final String DATE_TIME_FORMAT_ISO = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
	public static final String DATE_TIME_FORMAT_MIL_SEC = "yyyy-MM-dd HH:mm:ss.SSS";
	public static final String DATE_TIME_FORMAT_SEC = "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_TIME_FORMAT_MIN = "yyyy-MM-dd HH:mm";
	public static final String DATE_FORMAT = "yyyy-MM-dd";
	public static final String DATE_FORMAT_EMAIL = "ddMMyyyy";
	public static final String DATE_FORMAT_LONG_MONTH = "dd MMMM yyyy";
	public static final String TIME_FORMAT_SEC = "HH:mm:ss";
	public static final String TIME_FORMAT = "HH:mm";

	public static final String DATE_TIME_FORMAT_MON_IN = "dd MMM yyyy HH:mm";
	public static final String DATE_FORMAT_MON_IN = "dd-MMM-yyyy";

	public static final String DATE_TIME_FORMAT_SEC_WITHSLASH = "yyyy/MM/dd HH:mm:ss";

	public static final String DATE_TIME_FORMAT_SEC_IN = "dd/MM/yyyy HH:mm:ss";
	public static final String DATE_FORMAT_IN = "dd/MM/yyyy";
	public static final String DATE_FORMAT_DASH_IN = "dd-MM-yyyy";

	public static final String DATE_FORMAT_SDT_REPORT = "dd-MM-yyyy-HH-mm-ss";
	public static final String DATE_FORMAT_IMPORT_BM = "dd-MMM-yyyy HH:mm:ss.SSS";

	public static final String DEFAULT_DATE = "1990-01-01T00:00:00.000Z";

	public static final String POSTGRE_DATE_TIME_FORMAT_MON_IN = "dd Mon yyyy HH24:MI";
	public static final String POSTGRE_DATE_TIME_FORMAT_SEC = "YYYY-MM-DD HH24:MI:SS";
	public static final String POSTGRE_DATE_TIME_FORMAT_MIL_SEC = "YYYY-MM-DD HH24:MI:SS.MS";

	public static final String RKG_DATE_FORMAT = "yyyy/MM/dd";

	/*
	 * TIME FORMAT FOR DATE RANGE
	 */
	public static final String SOD_TIME = " 00:00:00"; // start of day
	public static final String EOD_TIME = " 23:59:59"; // end of day
	public static final String SOD_TIME_MILL_SEC = " 00:00:00.000"; // start of day
	public static final String EOD_TIME_MILL_SEC = " 23:59:59.999"; // end of day

	/*
	 * NUMBER FORMAT
	 */
	public static final String TWO_DEC_FORMAT = "%.2f";

	/*
	 * STATUS RESPONSE
	 */
	public static final int STATUS_CODE_SUCCESS = 0;
	public static final int STATUS_CODE_ALREADY_SIGNED = 10;
	public static final int STATUS_CODE_UNREGISTERED_USER = 20;
	public static final int STATUS_CODE_DOCUMENT_NOT_ACTIVE = 30;

	public static final String STATUS_DIGISIGN_SUCCESS = "00";
	public static final String STATUS_DIGISIGN_ERR_CODE = "05";

	/*
	 * TEMPLATE MESSAGE
	 */
	public static final String TEMPLATE_TYPE_SMS = "SMS";
	public static final String TEMPLATE_TYPE_WA = "WHATSAPP";

	// SMS & Email Template
	public static final String TEMPLATE_ACTIVATION = "AKTIVASI_PREREGISTER";
	public static final String TEMPLATE_REGISTER_NEWUSER_TTD = "REQ_TTD_USER_1ST";
	public static final String TEMPLATE_USER_TTD = "REQ_TTD_USER";
	public static final String TEMPLATE_REGISTER_NEWUSER_TTD_MNL = "REQ_TTD_MANUAL_1ST";
	public static final String TEMPLATE_USER_TTD_MANUAL = "REQ_TTD_MANUAL";
	public static final String TEMPLATE_VERIF = "VERIF_EMAIL";
	public static final String TEMPLATE_REMINDER_TOPUP = "REMINDER_TOPUP";
	public static final String TEMPLATE_RESET_PASSWORD = "RESET_PASSWORD";
	public static final String TEMPLATE_KODE_OTP = "OTP_VERIF_EMAIL";
	public static final String TEMPLATE_INFO_AKUN = "INFO_AKUN";
	public static final String TEMPLATE_INFO_AKUN_SMS = "INFO_AKUN_SMS";
	public static final String TEMPLATE_USER_TTD_SMS = "TTD_SMS";
	public static final String TEMPLATE_USER_TTD_SMS_1ST = "TTD_SMS_1ST";
	public static final String TEMPLATE_USER_TTD_SMS_MANUAL = "TTD_SMS_MANUAL";
	public static final String TEMPLATE_USER_TTD_SMS_MANUAL_1ST = "TTD_SMS_MANUAL_1ST";
	public static final String TEMPLATE_CHANGE_EMAIL = "OTP_CHGEMAIL_SMS";
	public static final String TEMPLATE_RESET_PASS_DIGI = "RESET_PASS_DIGI_SMS";
	public static final String TEMPLATE_RST_PASS_TKNAJA_SMS = "RST_PASS_TKNAJA_SMS";
	public static final String TEMPLATE_RESET_PASS_ESIGN = "RESET_PASS_ESIGN_SMS";
	public static final String TEMPLATE_SEND_NOTIF_TESTING = "SEND_NOTIF_TESTING_SMS";
	public static final String TEMPLATE_RESET_PASS_ESIGN_EXPIRED = "RESET_PASS_ESIGN_SMS_EXPIRED";
	public static final String TEMPLATE_INSUFFICIENT_BAL = "INSUFFICIENT_BAL";
	public static final String TEMPLATE_INV_REG = "INV_REG";
	public static final String TEMPLATE_INV_REG_EXP = "INV_REG_EXP";
	public static final String TEMPLATE_INV_REG_SMS = "INV_REG_SMS";
	public static final String TEMPLATE_INV_REG_SMS_EXP = "INV_REG_SMS_EXP";
	public static final String TEMPLATE_KODE_OTP_SMS = "OTP_VERIF_NO";
	public static final String TEMPLATE_ATTACH_EMETERAI_ERROR = "ATTACH_SDT_ERROR";
	public static final String TEMPLATE_FWD_EMAIL_ACTIVATION = "FWD_EMAIL_ACTIVATION";
	public static final String TEMPLATE_ACT_TEKEN = "FWD_EMAIL_ACTIVATION";
	public static final String TEMPLATE_PIN_TEKENAJA_SMS = "PIN_TEKENAJA_SMS";
	public static final String TEMPLATE_REREGIS_DIGI = "REREGIS_DIGI";
	public static final String TEMPLATE_TTD_MANUAL_SELESAI = "TTD_MANUAL_SELESAI";
	public static final String TEMPLATE_NOTIF_AUTOSIGN = "NOTIF_AUTOSIGN";
	public static final String TEMPLATE_DOCH_SIGN_COMPLETE = "DOCH_SIGN_COMPLETE";
	public static final String TEMPLATE_DOCH_CAN_STAMP = "DOCH_CAN_STAMP";
	public static final String TEMPLATE_OTP_SMS_WITH_DURATION = "OTP_SMS_WITH_DURATION";
	public static final String TEMPLATE_OTP_VERIF_EMAIL_WITH_DURATION = "OTP_VERIF_EMAIL_WITH_DURATION";
	public static final String TEMPLATE_EMAIL_PENERBITAN_SERTIFIKAT_ELEKTRONIK = "EMAIL_PENERBITAN_SERTIFIKAT_ELEKTRONIK";
	public static final String TEMPLATE_SMS_PENERBITAN_SERTIFIKAT_ELEKTRONIK = "SMS_PENERBITAN_SERTIFIKAT_ELEKTRONIK";
	public static final String TEMPLATE_RETRY_RESUME_WORKFLOW_FAIL = "RETRY_RESUME_WORKFLOW_FAIL";
	public static final String TEMPLATE_TOPUP_SUCCESS = "TOPUP_SUCCESS";
	public static final String TEMPLATE_SEND_EMAIL_FAIL = "SEND_EMAIL_FAIL";
	public static final String TEMPLATE_SEND_EMAIL_EXTEND_BALANCE_SUCCESS = "SEND_EMAIL_EXTEND_BALANCE_SUCCESS";

	// WhatsApp Template
	public static final String TEMPLATE_OTP_WA = "otp_wa";
	public static final String TEMPLATE_OTP_WA_WITH_DURATION = "otp_wa_with_duration";
	public static final String TEMPLATE_SEND_NOTIFICATION_TESTING = "send_testing_notification";

	/*
	 * DOCUMENT
	 */
	public static final String DEFAULT_DOCUMENT_ID = "DOC00000";
	public static final String PREFIX_DOCUMENT_ID = "DOC";
	public static final String PREFIX_DOCUMENT_FILE_NAME = "DOC_";

	/*
	 * IMAGE
	 */
	public static final String PREFIX_PHOTO_SELF_FILE_NAME = "SELF_";
	public static final String PREFIX_PHOTO_ID_FILE_NAME = "KTP_";
	public static final String PREFIX_PHOTO_NPWP_FILE_NAME = "NPWP_";
	public static final String PREFIX_TTD_FILE_NAME = "TTD_";
	public static final String PREFIX_PHOTO_SELFIE_FILE_NAME = "SELFIE_";

	/*
	 * ObjectName
	 */

	public static final String OBJECT_NAME_VENDOR_CODE = "Vendor Code";

	public static final String OBJECT_NAME_TENANT_CODE = "Tenant Code";

	/*
	 * Archive Status
	 */
	public static final String ARCHIVE_STATUS_ACTIVE = "Active";
	public static final String ARCHIVE_STATUS_ARCHIVE_VALUE = "1";
	public static final String ARCHIVE_STATUS_ARCHIVE = "Archive";
	public static final String ARCHIVE_STATUS_RESTORED_VALUE = "2";
	public static final String ARCHIVE_STATUS_RESTORED = "Restored";

	/*
	 * USER
	 */
	public static final String USER_ADMIN_LOGIN_ID = "SYSADMIN";

	/*
	 * VENDOR
	 */
	public static final String VENDOR_CODE_ESG = "ESG";

	/*
	 * VENDOR PSRE
	 */
	public static final String VENDOR_CODE_DIGISIGN = "DIGI";
	public static final String VENDOR_CODE_DIRJENPAJAK = "DJP";
	public static final String VENDOR_CODE_PRIVY_ID = "PRIVY";
	public static final String VENDOR_CODE_VIDA = "VIDA";
	public static final String VENDOR_CODE_PERURI = "PERURI";
	public static final String VENDOR_CODE_DJELAS = "DJELAS"; // AsliRi/TekenAja/Djelas Tandatangan Bersama
	public static final String VENDOR_CODE_TEKENAJA = "TKNAJ";
	public static final String VENDOR_CODE_JATIS = "JATIS";
	public static final String VENDOR_CODE_HALOSIS = "HALOSIS";
	public static final String VENDOR_CODE_VFIRST = "VFIRST";

	/*
	 * TENANT CODE
	 */
	public static final String TENANT_CODE_WOMF = "WOMF";
	public static final String TENANT_CODE_CFI = "CFI";
	public static final String TENANT_CODE_LIVENESS_FACECOMPARE = "ESIGNHUB";

	/*
	 * DOCUMENT TYPE
	 */
	public static final String DOC_TYPE_AGREEMENT = "AGR";
	public static final String DOC_TYPE_GENERAL = "GENERAL";
	public static final String DOCUMENT_TYPE = "Document type";

	/*
	 * NOTIFICATION TYPE
	 */
	public static final String NOTIF_TYPE_EMAIL = "EMAIL";
	public static final String NOTIF_TYPE_SMS = "SMS";

	/*
	 * PRIVY GENERAL OTP
	 */
	public static final String PRIVY_GENERAL_REGISTRATION_OTP_TYPE = "0";
	public static final String PRIVY_GENERAL_DOC_SIGNING_OTP_TYPE = "1";
	public static final String PRIVY_GENERAL_OTP_CHANNEL = "0";

	/*
	 * FACE VERIFY SERVICE
	 */
	public static final String FACE_VERIFY_SERVICE_ACTIVE = "1";
	public static final String FACE_VERIFY_SERVICE_INACTIVE = "0";
	public static final String FACE_VERIFY_SERVICE_ERROR_SELFIE_SPOOF = "Selfie Spoof";

	/*
	 * LIVENESS VERIFY STATUS
	 */
	public static final String STATUS_VERIFY_SUCCESS = "Success";
	public static final String STATUS_VERIFY_FAILED = "Failed";

	/*
	 * LIVENESS CHECK STATUS
	 */
	public static final String STATUS_LIVENESS_CHECK_SUCCESS = "1";
	public static final String STATUS_LIVENESS_CHECK_FAILED = "0";

	public static final String STACK_TRACE = "Stack trace: {}";

	// OSS Cloud storage key format
	public static final String OSS_PREFIX_ENCRYPTED = "encrypted_";
	public static final String PERSONAL_KTP_FILENAME_FORMAT = "pd/%1$s/%1$s_ktp"; // nik,nik
	public static final String PERSONAL_SELFIE_FILENAME_FORMAT = "pd/%1$s/%1$s_selfie.jpg"; // nik,nik
	public static final String TRX_SELFIE_FILENAME_FORMAT = "tx/%1$s/%2$s/%3$s/selfie_%4$s_%5$s.jpg"; // tenant,nik,date,refNumber,timestamp
	public static final String DOCUMENT_PDFDOC_FORMAT = "pdfdoc/%1$s"; // refNumber
	public static final String DOCUMENT_PDFDOC_DETAIL_FORMAT = "pdfdoc/%1$s/%2$s.pdf"; // refNumber, documentId
	public static final String DOCUMENT_STAMPINGDOC_FORMAT = "stamping_eMaterai/%1$s/%2$s/%3$s.pdf"; // tenantCode,
																										// refNumber,
																										// documentId
	public static final String MANUAL_STAMP_FORMAT = "stamping_ematerai/%1$s/%2$s/%3$s.pdf"; // year, month, documentId
	public static final String DOCUMENT_STAMPING_RESULT_DETAIL_FORMAT = "stamping_result/%1$s/%2$s.pdf"; // tenantCode,
																											// refNumber
	public static final String STAMPED_DOCUMENT_FORMAT = "stamping_result/%1$s/%2$s/%3$s.pdf"; // year, month,
																								// documentId
	public static final String DOCUMENT_SIGN_COMPLETE_FORMAT = "sign_complete/%1$s/%2$s/%3$s/%4$s.pdf";
	public static final String PAYMENT_RECEIPT_STAMPING_FORMAT = "stamping_document/base/%1$s/%2$s/%3$s.pdf"; // year,
																												// month,
																												// documentId
	public static final String BASE_SIGN_DOCUMENT_FORMAT = "signing_document/%1$s/%2$s/%3$s.pdf";
	public static final String SIGNED_DOCUMENT_FORMAT = "sign_complete/%1$s/%2$s/%3$s.pdf";
	public static final String DOWNLOAD_MANUAL_REPORT = "manual_report/%1$s/%2$s";
	public static final String USER_SELFIE_FORMAT = "selfie_user/%1$s/%2$s/%3$s.jpeg";
	public static final String REGISTER_REQUEST_FORMAT = "register_request/%1$s/%2$s/%3$s.txt";
	public static final String DOWNLOAD_CERTIFICATE_FORMAT = "certificate_tekenaja/%1$s.crt";
	public static final String AUTOSIGN_BM_EXCEL_UPLOAD = "import_bm/%1$s/%2$s/%3$s-%4$s-%5$s";
	public static final String TEMPLATE_EXCEL_AUTOSIGN_BM = "import_bm/templateImport.xlsx";
	public static final String AUDIT_TRAIL_API_LOG_FORMAT = "audit_log/%1$s/%2$s.zip";
	public static final String AUDIT_TRAIL_SELFIE_PHOTO = "audit_log/liveness_face_compare/%1$s.zip";
	public static final String AUDIT_TRAIL_PRIVY_SIGN_LOG = "audit_log/signing_process/%1$s.zip";
	public static final String REPORT_MANUAL_UPLOAD = "manual_report/%1$s/%2$s";
	public static final String POA_CUSTOM_SIGN = "poa_custom_sign/%1$s/%2$s.jpg";
	public static final String MANDATORY_KEY = "Key can't be null";
	/*
	 * DELETE EMAIL TYPE
	 */
	public static final String DELETE_EMAIL_TYPE_ALL = "ALL";
	public static final String DELETE_EMAIL_TYPE_READ = "READ";
	public static final String DELETE_EMAIL_TYPE_DATE = "DATE";

	/*
	 * READ EMAIL DATA TYPE
	 */
	public static final String READ_EMAIL_TYPE_TEXT = "TEXT";
	public static final String READ_EMAIL_TYPE_LINK = "LINK";

	/*
	 * HASH METHOD
	 */
	public static final String HASH_SHA = "SHA-256";

	/*
	 * TENANT SETTINGS PARAM
	 */
	public static final String TENANT_SETTING_REF_NO_LABEL = "REF_NO_LABEL";

	/*
	 * DIGISIGN BALANCE TYPE
	 */
	public static final String DIGI_BALANCE_TYPE_SMS = "sms";
	public static final String DIGI_BALANCE_TYPE_VRF = "verifikasi";
	public static final String DIGI_BALANCE_TYPE_DOC = "dokumen";

	// Stamping Status
	public static final String STATUS_ATTACH_METERAI_FAILED = "1";
	public static final String STATUS_ATTACH_METERAI_ERROR = "2";
	public static final String STATUS_ATTACH_METERAI_FINISHED = "3";
	public static final String STATUS_ATTACH_METERAI_PROCESS = "5";
	public static final String STATUS_MCP_SUCCESS = "00";

	// Payment Receipt Stamping Status
	public static final String PR_STAMP_FAILED = "321";
	public static final String PR_STAMP_IN_QUEUE = "322";
	public static final String PR_STAMP_SUCCESS = "323";
	public static final String PR_STAMP_IN_PROGRESS = "325";

	// On Premise Stamping Status
	public static final String ON_PREM_STAMP_FAILED = "51";
	public static final String ON_PREM_STAMP_IN_QUEUE = "52";
	public static final String ON_PREM_STAMP_SUCCESS = "53";
	public static final String ON_PREM_STAMP_IN_PROGRESS = "55";

	// Privy Stamping Status
	public static final String ON_PREM_STAMP_FAILED_PRIVY = "61";
	public static final String ON_PREM_STAMP_IN_QUEUE_PRIVY = "62";
	public static final String PRIVY_STAMP_SUCCESS = "63";

	// VIDA Stamping Status
	public static final String VIDA_STAMP_FAILED = "71";
	public static final String VIDA_STAMP_IN_QUEUE = "72";
	public static final String VIDA_STAMP_SUCCESS = "73";
	public static final String VIDA_STAMP_REQUEST_PROCESSED = "74";
	public static final String VIDA_STAMP_IN_PROGRESS = "75";

	// On Premise Payment Receipt Stamping Status
	public static final String ON_PREM_PR_STAMP_FAILED = "521";
	public static final String ON_PREM_PR_STAMP_IN_QUEUE = "522";
	public static final String ON_PREM_PR_STAMP_SUCCESS = "523";
	public static final String ON_PREM_PR_STAMP_IN_PROGRESS = "525";

	/*
	 * STEPS ATTACH METERAI
	 */
	public static final String STEP_ATTACH_METERAI_NOT_STR = "NOT_STR";
	public static final String STEP_ATTACH_METERAI_SDT_STR = "SDT_STR";
	public static final String STEP_ATTACH_METERAI_UPL_DOC = "UPL_DOC";
	public static final String STEP_ATTACH_METERAI_CRT_ORD = "CRT_ORD";
	public static final String STEP_ATTACH_METERAI_UPD_DOC = "UPD_DOC";
	public static final String STEP_ATTACH_METERAI_GEN_SDT = "GEN_SDT";
	public static final String STEP_ATTACH_METERAI_STM_SDT = "STM_SDT";
	public static final String STEP_ATTACH_METERAI_UPL_OSS = "UPL_OSS";
	public static final String STEP_ATTACH_METERAI_SDT_FIN = "SDT_FIN";
	public static final String STEP_ATTACH_METERAI_UPL_CON = "UPL_CON";
	public static final String STEP_ATTACH_METERAI_NOT_SDT = "NOT_SDT";

	/*
	 * INVITATION LINK
	 */
	public static final String INV_BY_EMAIL = "Email";
	public static final String INV_BY_SMS = "SMS";

	/*
	 * CALLBACK PROCESS
	 */
	public static final Short CALLBACK_PROCESS_NOT_STARTED = 0;
	public static final Short CALLBACK_PROCESS_FAIL_CALL_RESULT_URL = 1;
	public static final Short CALLBACK_PROCESS_SUCCESS_CALL_RESULT_URL = 2;
	public static final Short CALLBACK_PROCESS_FAIL_UPLOAD_CONFINS = 3;
	public static final Short CALLBACK_PROCESS_SUCCESS_UPLOAD_CONFINS = 4;
	public static final Short CALLBACK_PROCESS_SUCCESS_FINAL_UPLOAD_CONFINS = 5;

	/*
	 * RKG
	 */
	public static final String RKG_DOC_TYPE_PERJANJIAN = "Surat Perjanjian";
	public static final String RKG_CERTIF_LVL_NOT_CERTIFIED = "NOT_CERTIFIED";
	public static final String RKG_REASON_APPROVAL = "approval";

	/*
	 * Pajakku
	 */
	public static final String PERURI_SUCCESS_CODE = "00";
	public static final String PERURI_ERROR_CODE = "01";
	public static final String PERURI_STAMPING_SUCCESS_STATUS = "True";
	public static final String PAJAKKU_NILAI_METERAI_LUNAS = "10000";

	/*
	 * Hasil Stamping
	 */
	public static final String HASIL_STAMPING_NOT_STARTED = "Not Started";
	public static final String HASIL_STAMPING_FAILED = "Failed";
	public static final String HASIL_STAMPING_SUCCESS = "Success";
	public static final String HASIL_STAMPING_IN_PROGRESS = "In Progress";

	/*
	 * Sumber Biometrik
	 */
	public static final String SUMBER_BIOMETRIK_ASLIRI = "Asli RI";

	/*
	 * Caller Id for Embed
	 */
	public static final String CALLER_ID_MONITORING = "MONITORING";

	/*
	 * Error type for TrErrorHistory
	 */
	public static final String ERROR_TYPE_REJECT = "REJECT";
	public static final String ERROR_TYPE_ERROR = "ERROR";

	/*
	 * TekenAja Callback Code
	 */
	public static final String CALLBACK_REGISTRASI_CODE = "REGISTRATION_COMPLETE";
	public static final String CALLBACK_DOCUMENT_SIGN_CODE = "DOCUMENT_SIGNED";
	public static final String CALLBACK_DOCUMENT_SIGN_COMPLETE_CODE = "DOCUMENT_SIGN_COMPLETE";

	/*
	 * Kode Registrasi Cek Teken Aja
	 */
	public static final String TEKEN_REGCEK_USER_EXIST_VERIFIED = "USER_EXISTS_VERIFIED";
	public static final String TEKEN_REGCEK_USER_DO_NOT_EXISTS = "USER_DO_NOT_EXISTS";
	public static final String TEKEN_REGCEK_USER_EXISTS_UNVERIFIED = "USER_EXISTS_UNVERIFIED";
	public static final String TEKEN_REGCEK_USER_EXISTS_CERTIVICATE_EXPIRED = "USER_EXISTS_CERTIVICATE_EXPIRED";

	public static final String EMAIL_PATTERN_FORWARD = "Forward";
	public static final String EMAIL_PATTERN_SHORTEN_FORWARD = "Shortern & Forward";
	public static final String EMAIL_PATTERN_GET_DATA = "Get Data";

	/*
	 * Teken Aja Hash Sign
	 */
	public static final String TEKEN_AJA_HASH_SIGN_USER_NOT_FOUND = "USER_NOT_FOUND";
	public static final String TEKEN_AJA_HASH_SIGN_INVALID_OTP = "OTP_INVALID";
	public static final String TEKEN_AJA_HASH_SIGN_OTP_EXPIRED = "OTP_EXPIRED";
	public static final String TEKEN_AJA_HASH_SIGN_SYSTEM_FAILURE = "SYSTEM_FAILURE";

	/*
	 * Tax Type
	 */
	public static final String TAX_TYPE_PEMUNGUT = "Pemungut";
	public static final String TAX_TYPE_NON_PEMUNGUT = "Non Pemungut";

	/*
	 * Job Type
	 */
	public static final String JOB_TYPE_JOB_PROCESS = "Job Process";
	public static final String JOB_TYPE_JOB_REKONSIL = "RECON_OTP";

	/*
	 * Character generate password
	 */
	public static final String CHRS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String LOWER_CHRS = "abcdefghijklmnopqrstuvwxyz";
	public static final String UPPER_CHRS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	public static final String NUMBER = "0123456789";

	/*
	 * Register NIK verification status
	 */
	public static final String NIK_UNREGISTERED = "unregistered";
	public static final String NIK_REGISTERED = "registered";

	public static final String CONST_TRUE = "true";
	public static final String CONST_FALSE = "false";

	/*
	 * Sent OTP Variable
	 */
	public static final String DURATION = "duration";
	public static final String SEND_OTP_SMS = " : Send OTP SMS";
	public static final String SEND_OTP_SMS_SIGNING = " : Send OTP SMS Signing Request";
	public static final String SEND_OTP_WA_SIGNING = " : Send OTP WhatsApp Signing Request";
	public static final String SEND_OTP_WA_FORGOT_PASSWORD = " : Send OTP WhatsApp Forgot Password Esign";
	public static final String SEND_WA_NOTIF_TESTING = " : Send Notif WA for Testing";
	public static final String SEND_SMS_NOTIF_TESTING = " : Send Notif SMS for Testing";

	// User Data Access Log Detail
	public static final String DATAACCESS_LOG_REQDETAIL_VIEW_RESET_CODE = "View Reset Code";
	public static final String DATAACCESS_LOG_REQDETAIL_EDIT_SIGNER_DATA = "Edit Signer Data";

	/*
	 * Insert BalanceMutation error
	 */
	public static final String BALMUT_ERROR = " error ";

	// Transaction status
	public static final String TRANSACTION_STATUS = "Transaction status";

	// CONST STRING LITERAL
	public static final String CONST_REQUEST = "Request";
	public static final String CONST_COMPLETED = "Completed";
	public static final String CONST_RETRY_STAMPING_START = "Proses retry stamping dimulai";
	public static final String CONST_DOCUMENT_ID = "documentId";
	public static final String CONST_PROSES_TTD = "Proses TTD";
	public static final String CONST_TRANSAKSI = "transaksi";
	public static final String CONST_DOCUMENT_TEMPLATE_CODE = "documentTemplateCode";
	public static final String CONST_VERIFIKASI_WAJAH_GAGAL = "Verifikasi wajah gagal";
	public static final String CONST_VERIFIKASI_USER_GAGAL_TIDAK_DITEMUKAN_FOTO_WAJAH = "Verifikasi user gagal. Tidak ditemukan foto wajah pada foto selfie";
	public static final String CONST_TENANT = "tenant";
	public static final String CONST_TENANT_CODE = "tenantCode";
	public static final String CONST_VENDOR_CODE = "vendorCode";
	public static final String CONST_EMAIL = "email";
	public static final String CONST_CONFINS = "CONFINS";
	public static final String CONST_X_API_KEY = "x-api-key";
	public static final String CONST_API_KEY = "apikey";
	public static final String CONST_ENDPOINT_IS_DEPRECATED = "Endpoint is Deprecated";
	public static final String CONST_ELECTRONIC_CERTIFICATE_EXPIRED_STATUS = "Expired";
	public static final String CONST_INVITATION_LINK_ACTIVE_DURATION = "invitation_link_active_duration";
	public static final String CONST_INVITATION_LINK_BUTTON = "inv_link_button2";
	public static final String CONST_LIVENESS_FACECOMPARE_RESPONSE = "Liveness face compare response";
	public static final String CONST_RESEND_SIGN_REQUEST_VIA_WA_SEND = "Resend sign request via WA Sent.";
	public static final String CONST_VALIDATE_STAMP_ID_TYPE_PARAM = "idType";
	public static final String CONST_ERROR_MESSAGE_GENERATE_AND_SEND_REG_INV_LINK_ERROR = "Generate And Send RegInv Link V2 Error";
	public static final String CONST_REQUEST_TIME_START = "Request Time Start";
	public static final String CONST_REQUEST_TIME_END = "Request Time End";
	public static final String CONST_REPORT_TIME_START = "Report Time Start";
	public static final String CONST_REPORT_TIME_END = "Report Time End";
	public static final String CONST_STAMP_PAYMENT_TYPE_TIPE_PEMBAYARAN = "Tipe Pembayaran";
	public static final String CONST_OBJECT_ID_INVITATION_LINK = "idInvitationLink";
	public static final String CONST_ARCHIVE = "Archive";
	public static final String CONST_RESTORE = "Restored";
	public static final String CONST_ACTIVE = "Active";

	// IP Addresses
	public static final String IP_ADDRESS_LOCAL = "127.0.0.1";

	// Embed Url Param
	public static final String CONST_URL_PATH_EMBED_DASHBOARD_SETTING = "&isMonitoring=true&isHO=1&tenantCode=ADINS&isOffice=false";

	public static final String NOTIFICATION_VENDOR_NAME_SMS_VFIRST = "Value First";
	public static final String NOTIFICATION_VENDOR_NAME_SMS_JATIS = "Jatis";
	public static final String NOTIFICATION_VENDOR_NAME_SMS_PRIVY = "SMS Privy";
	public static final String NOTIFICATION_VENDOR_NAME_WHATSAPP_JATIS = "Jatis";
	public static final String NOTIFICATION_VENDOR_NAME_WHATSAPP_HALOSIS = "Halosis";

	// Audit trail log subfolder names
	public static final String AUDIT_TRAIL_SUBFOLDER_REGISTER = "registration_data";
	public static final String AUDIT_TRAIL_SIGNING_PROCESS = "signing_process";

	// WA Status
	public static final String WA_STATUS_SUCESS = "success";
}
