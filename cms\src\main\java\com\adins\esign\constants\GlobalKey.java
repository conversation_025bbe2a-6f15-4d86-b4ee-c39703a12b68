package com.adins.esign.constants;

public class GlobalKey {
	/**
	 * The class shall not be instantiated.
	 * This constructor will throw IllegalStateException when instantiated.
	 */
	protected GlobalKey() {
		throw new IllegalStateException("GlobalKey class shall not be instantiated! Class=" + this.getClass().getName());
	}


	/**
	 * Key for for storing exception error code, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_EXCEPTION = "excp";

	/**
	 * Key for for storing last action result, specified in as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_LAST_RESULT = "lastResult";

	/**
	 * Key for for storing default previous parameter for redirection, as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_PARAM_MAP = "redir.params";

	/**
	 * Key for for storing default target for redirection, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_REDIR_TARGET = "redir.target";

	/**
	 *  Key for for storing state, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_STATE = "state";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_ACTION_NAME = "actionName";

	/**
	 *  Key for for storing mode, specified as struts interceptor parameter value.
	 */
	public static final String ATTRIBUTE_MODE = "mode";

	/**
	 * Property key for <i>other</i> context factory.
	 */
	public static final String CONTEXT_FACTORY = "context.factory";

	/**
	 * Property key for <i>other</i> Context URL provider.
	 */
	public static final String CONTEXT_URL = "context.url";

	/**
	 * Property key for initializing group of Context Class locations.
	 */
	public static final String CONTEXT_INITIALIZER_CLASS = "initializerClassLocation";
	
	/**
	 * Property key for database (datasource) JNDI name.
	 */
	public static final String JNDI_DATASOURCE = "jndi.datasource";

	/**
	 * Key for for storing result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_LIST = "resultList";

	/**
	 * Key for for storing size of result list object, used in map for storing query result.
	 */
	public static final String MAP_RESULT_SIZE = "resultSize";

	/**
	 * Key for for storing result list object with that version, used in map for storing query result.
	 */
	public static final String MAP_RESULT_VERSION = "resultVersion";

	/**
	 * String that is used for storing action message.
	 */
	public static final String SESSION_ACTION_MESSAGE = "SESSION_ACTION_MESSAGE";

	/**
	 *Key for storing current parameter information in application session.
	 */
	public static final String SESSION_CURR_PARAMETER = "SESSION_CURR_PARAMETER";

	/**
	 *Key for storing login information in application session.
	 */
	public static final String SESSION_LOGIN = "SESSION_LOGIN";

	/**
	 * String that is used for storing model version for concurrency checking.
	 */
	public static final String SESSION_MODEL_VERSION = "SESSION_MODEL_VERSION";

	/**
	 *Key for storing push parameter information in application session.
	 */
	public static final String SESSION_PUSH_PARAMETER = "SESSION_PUSH_PARAMETER";

	/**
	 *Key for storing temporary parameter information in application session.
	 */
	public static final String SESSION_TEMP_PARAMETER = "SESSION_TEMP_PARAMETER";

	/**
	 *Key for specifying location of file application properties when using in system environment.
	 */
	public static final String SYSTEM_ENVIRONMENT = "application.properties";

	public static final String LOGIN_BYPASS = "login.bypass";
	
	public static final String LDAP_ENABLED = "ldap.enabled";
	public static final String LDAP_HOST = "ldap.host";
	public static final String LDAP_PORT = "ldap.port";
	public static final String LDAP_DOMAIN = "ldap.domain";

	public static final String TASK_D_TYPE = "taskd.type";
	
	public static final String INTERFACE_TYPE = "interface.type";
	
	public static final String STAGING_URI="staging.uri";
	public static final String STAGING_URI_KONVERGEN="staging.uri.konvergen";
	public static final String DIGISIGN_URI="digisign.uri";
	public static final String PREREGISTER_URI="preregister.uri";
	public static final String ESIGN_ACTIVATION_URI="esign.activation.uri";

	public static final String NC_URI="NC.URI";
	public static final String PUBLIC_URI="public.uri";
	public static final String WS_BASE="ws.base";
	public static final String WS_BASE_PUBLIC="ws.base.public";
	public static final String APP_BASE="app.base";
	public static final String APP_BASE_JSP="app.base.jsp";
	public static final String APP_BASE_JSP_PUBLIC="app.base.jsp.public";
	public static final String IS_SECURE="is.secure";
	
	public static final String AUDIT_KEY_LOCALE = "locale";
	
	public static final String MESSAGE_INFO_COMMON_CONFIRMADD = "info.common.confirmadd";
	public static final String MESSAGE_INFO_COMMON_CONFIRMDELETE = "info.common.confirmdelete";
	public static final String MESSAGE_INFO_COMMON_DATAEXISTS = "info.common.dataexists";
	public static final String MESSAGE_INFO_COMMON_DBERROR = "info.common.dberror";
	public static final String MESSAGE_INFO_COMMON_ERROR = "info.common.error";
	public static final String MESSAGE_INFO_COMMON_HASCHILD = "info.common.haschild";
	public static final String MESSAGE_INFO_COMMON_RECORDCONCURRENCY = "info.common.recordconcurrency";
	public static final String MESSAGE_INFO_COMMON_STACKTRACE = "info.common.stacktrace";
	
	public static final String MESSAGE_ERROR_GLOBAL_EMPTY_PARAM	= "service.global.emptyparam";
	public static final String MESSAGE_ERROR_GLOBAL_EXISTED = "service.global.existed";
	public static final String MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND = "businesslogic.global.entitynotfound";
	public static final String MESSAGE_ERROR_GLOBAL_MANDATORY = "businesslogic.global.mandatory";
	public static final String MESSAGE_ERROR_GLOBAL_INCORRENT_DATE_FORMAT = "businesslogic.global.dateformat";
	public static final String MESSAGE_ERROR_GLOBAL_NUMERIC = "businesslogic.global.numeric";
	public static final String MESSAGE_ERROR_GLOBAL_ACTION = "businesslogic.global.action";
	public static final String MESSAGE_ERROR_GLOBAL_ACTIVEDEL = "businesslogic.global.activeDel";
	public static final String MESSAGE_ERROR_GLOBAL_DATANOTFOUND = "businesslogic.global.datanotfound";
	public static final String MESSAGE_ERROR_GLOBAL_DATANOTFOUND_1 = "businesslogic.global.datanotfound1";
	public static final String MESSAGE_ERROR_GLOBAL_MUSTGREATERTHAN = "service.global.mustgreaterthan";
	public static final String MESSAGE_ERROR_GLOBAL_STATUS = "businesslogic.global.status";
	public static final String MESSAGE_ERROR_GLOBAL_INVALID_DATE_VALUE = "businesslogic.global.invaliddatevalue";
	
	public static final String MESSAGE_ERROR_COMMON_DATE = "error.common.date";
	public static final String MESSAGE_ERROR_COMMON_DOUBLE = "error.common.double";
	public static final String MESSAGE_ERROR_COMMON_INVALID = "error.common.invalid";
	public static final String MESSAGE_ERROR_COMMON_MASK = "error.common.mask";
	public static final String MESSAGE_ERROR_COMMON_MAXLENGTH = "error.common.maxlength";
	public static final String MESSAGE_ERROR_COMMON_MINLENGTH = "error.common.minlength";
	public static final String MESSAGE_ERROR_COMMON_RANGE = "error.common.range";
	public static final String MESSAGE_ERROR_COMMON_REQUIRED = "error.common.required";
	public static final String MESSAGE_ERROR_LOGIN_CONCURRENCE = "error.login.concurrence";
	public static final String MESSAGE_ERROR_LOGIN_DORMANT = "error.login.dormant";
	public static final String MESSAGE_ERROR_LOGIN_INACTIVE = "error.login.inactive";
	public static final String MESSAGE_ERROR_LOGIN_INVALID = "error.login.invalid";
	public static final String MESSAGE_ERROR_LOGIN_LOCKED = "error.login.locked";
	public static final String MESSAGE_ERROR_LOGIN_PWDEXPIRED = "error.login.pwdexpired";

	public static final String MESSAGE_ERROR_LOGIC_DATANOTEXIST = "businesslogic.global.datanotexist";
	public static final String MESSAGE_ERROR_LOGIC_DATAISEXIST = "businesslogic.global.dataisexist";
	public static final String MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST = "businesslogic.global.paraminexist";
	public static final String MESSAGE_ERROR_LOGIC_MISSINGSIGNERTYPE = "businesslogic.document.missingsignertype";
	public static final String MESSAGE_ERROR_LOGIC_PARSE_DATE_ERROR = "businesslogic.error.errorparsedate";
	public static final String MESSAGE_ERROR_LOGIC_DATE_FORMAT_DOES_NOT_MATCH = "businesslogic.error.parsedateformat";
	public static final String MESSAGE_ERROR_LOGIC_DATE_EXCEED_LIMIT = "businesslogic.error.invaliddatevalue";
	
	public static final String MESSAGE_ERROR_LOGIN_USER_DORMANT_1 = "businesslogic.login.dormantstatus1";
	
	public static final String MESSAGE_ERROR_USER_NOT_FOUND					= "businesslogic.user.usernotfound";
	public static final String MESSAGE_ERROR_USER_INVALID_LOGIN_ID			= "businesslogic.user.invalidloginid";
	public static final String MESSAGE_ERROR_USER_INACTIVE_OR_NOT_EXIST		= "businesslogic.user.inactiveuser";
	public static final String MESSAGE_ERROR_USER_INVALID_NEW_PASS			= "businesslogic.user.invalidnewpassword";
	public static final String MESSAGE_ERROR_USER_NEW_PASS_USED				= "businesslogic.user.passwordhasbeenused";
	public static final String MESSAGE_ERROR_USER_INCORRECT_RESET_CODE		= "businesslogic.user.incorrectresetcode";
	public static final String MESSAGE_ERROR_USER_ALREADY_REGISTERED		= "businesslogic.user.alreadyregistered";
	public static final String MESSAGE_ERROR_USER_NOT_REGISTERED		    = "businesslogic.user.notregistered";
	public static final String MESSAGE_ERROR_USER_WRONG_OTP_CODE			= "businesslogic.user.invalidotpcode";
	public static final String MESSAGE_ERROR_USER_EXPIRED_OTP_CODE			= "businesslogic.user.expiredotpcode";
	public static final String MESSAGE_ERROR_USER_PERSONAL_DATA_NOT_FOUND	= "businesslogic.user.personaldatanotfound";
	public static final String MESSAGE_ERROR_USER_INCORRECT_RESET_PASSWORD_LINK_CODE = "businesslogic.user.incorrectresetpasswordlinkcode";
	public static final String MESSAGE_ERROR_USER_PHONE_ALREADY_EXISTED		= "businesslogic.user.phonealreadyregistered";
	public static final String MESSAGE_ERROR_USER_EMAIL_ALREADY_EXISTED		= "businesslogic.user.emailalreadyregistered";
	public static final String MESSAGE_ERROR_USER_INVALID_PARAM				= "businesslogic.user.invalidparam";
	public static final String MESSAGE_ERROR_USER_VERIFY_NOT_MATCH			= "businesslogic.user.verifynotmatch";
	public static final String MESSAGE_ERROR_USER_EMAIL_NOT_FOUND			= "businesslogic.user.emailnotfound";
	public static final String MESSAGE_ERROR_USER_ALREADY_ACTIVATED         = "businesslogic.user.alreadyactivated";
	public static final String MESSAGE_ERROR_USER_CAN_REREGISTER			= "businesslogic.user.canreregister";
	public static final String MESSAGE_ERROR_USER_NOT_REGISTERED_IN_VENDOR	= "businesslogic.user.usernotregisteredinvendor";
	public static final String MESSAGE_ERROR_USER_PHONE_EMPTY				= "businesslogic.user.phonenoisempty";
	public static final String MESSAGE_ERROR_USER_PHONE_INV_NOT_MATCH		= "businesslogic.user.phonenotmatchwithinvitation";
	public static final String MESSAGE_ERROR_USER_PHONE_NOT_FOUND			= "businesslogic.user.usernotfoundwiththatphoneno";
	public static final String MESSAGE_ERROR_USER_VENDOR_CODE_EMPTY			= "businesslogic.user.vendorcodeempty";
	public static final String MESSAGE_ERROR_USER_VENDOR_NAME_EMPTY			= "businesslogic.user.vendornameempty";
	public static final String MESSAGE_ERROR_USER_NOT_FOUND_WITH_EMAIL		= "businesslogic.user.usernotfoundwiththatemail";
	public static final String MESSAGE_ERROR_USER_FACE_NOT_DETECTED 		= "businesslogic.user.facenotdetected";
	public static final String MESSAGE_ERROR_USER_MORE_THAN_ONE_FACE_DETECTED = "businesslogic.user.morethanonefacedetected";
	public static final String MESSAGE_ERROR_USER_FACE_OUT_OF_POSITION		= "businesslogic.user.faceoutofposition";
	public static final String MESSAGE_ERROR_USER_FACE_TOO_CLOSE			= "businesslogic.user.facetooclose";
	public static final String MESSAGE_ERROR_USER_FACE_TOO_FAR				= "businesslogic.user.facetoofar";
	public static final String MESSAGE_ERROR_USER_EYE_GLASSES_DETECTED 		= "businesslogic.user.eyeglassesdetected";
	public static final String MESSAGE_ERROR_USER_FACE_TOO_BRIGHT			= "businesslogic.user.facetoobright";
	public static final String MESSAGE_ERROR_BACKLIGHT_DETECTED				= "businesslogic.user.backlightdetected";
	public static final String MESSAGE_ERROR_USER_FACE_TOO_DARK 			= "businesslogic.user.facetoodark";
	public static final String MESSAGE_ERROR_USER_MASKING_FAILED 			= "businesslogic.user.maskingfailed";
	public static final String MESSAGE_ERROR_USER_VENDOR_CANNOT_BE_EMPTY	= "businesslogic.user.vendorcannotbeempty";
	public static final String MESSAGE_ERROR_USER_INVALID_EMAIL				= "businesslogic.user.invalidemail";
	public static final String MESSAGE_ERROR_USER_NIK_REGISTERED			= "businesslogic.user.nikregistered";
	public static final String MESSAGE_ERROR_USER_TENANT_NOT_FOUND			= "businesslogic.user.usertenantnotfound";
	public static final String MESSAGE_ERROR_USER_HASNOT_ACTIVATED			= "businesslogic.user.userhasnotactivated";
	public static final String MESSAGE_ERROR_NOT_FOUND                      = "businesslogic.notfound";
	public static final String MESSAGE_ERROR_USER_URL_FORWARDER_CODE_INVALID = "businesslogic.user.urlforwardercodeinvalid";
	public static final String MESSAGE_ERROR_USER_URL_FORWARDER_EXPIRED = "businesslogic.user.urlforwarderexpired";
	public static final String MESSAGE_ERROR_USER_PHONE_NOT_REGISTERED_WITH_EMAIL = "businesslogic.user.userwithphonenotregisteredwithemail";
	public static final String MESSAGE_ERROR_USER_DOES_NOT_HAVE_ANY_DOC = "businesslogic.user.userdoesnothaveanydocument";
	public static final String MESSAGE_ERROR_USER_CERTIFICATE_ACTIVE_STATUS_EXPIRED		= "businesslogic.user.usercertificateactivestatusexpired";
	public static final String MESSAGE_ERROR_USER_INVALID_NIK_LENGTH = "businesslogic.user.invalidniklength";
	public static final String MESSAGE_ERROR_USER_PHONE_USER_NOT_REGISTERED_WITH_EMAIL = "businesslogic.user.userwithphonenotregisteredwithemail";

	public static final String MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC		= "businesslogic.document.usercannotaccessdoc";
	public static final String MESSAGE_ERROR_DOC_USER_CANNOT_ACCESS_DOC_1	= "businesslogic.document.usercannotaccessdoc1";
	public static final String MESSAGE_ERROR_DOC_CUST_INACCESSIBLE 			= "businesslogic.document.custdocumentinaccessible";
	public static final String MESSAGE_ERROR_DOC_BM_INACCESSIBLE			= "businesslogic.document.bmdocumentinaccessible";
	public static final String MESSAGE_ERROR_DOC_INVALID_DATE_RANGE			= "businesslogic.document.invaliddaterange";
	public static final String MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_GENERAL_SETTING = "businesslogic.document.invaliddaterangegeneralsetting";
	public static final String MESSAGE_ERROR_DOC_CANNOT_ACCESS_OTHER_TENANT	= "businesslogic.document.cannotaccessothertenant";
	public static final String MESSAGE_ERROR_DOC_NOT_YET_SIGNED_ALL 		= "businesslogic.document.documentnotsignedyet";
	public static final String MESSAGE_ERROR_DOC_ALRDY_SIGNED_ALL 			= "businesslogic.document.alreadysignedall";
	public static final String MESSAGE_ERROR_DOC_NOT_FOUND 			        = "businesslogic.document.documentnotfound";
	public static final String MESSAGE_ERROR_DOC_NOT_FOUND_1				= "businesslogic.document.docnotfound";
	public static final String MESSAGE_ERROR_DOC_ALRDY_SIGNED				= "businesslogic.document.alreadysigned";
	public static final String MESSAGE_ERROR_DOC_INVALID_DATE_RANGE_RECON			= "businesslogic.document.invaliddaterangerecon";
	public static final String MESSAGE_ERROR_DOC_MANDATORY_PARAM			= "businesslogic.document.mandatorycannotbeempty";
	public static final String MESSAGE_ERROR_DOC_REFNOEMPTY					= "businesslogic.document.referencenoempty";
	public static final String MESSAGE_ERROR_DOC_AGRNOT_FOUND_IN_TENANT_2	= "businesslogic.document.agreementnotfoundintenant2";
	public static final String MESSAGE_ERROR_DOC_DOCUMENT_ID_NOT_FOUND		= "businesslogic.document.documentidnotfound";
	public static final String MESSAGE_ERROR_DOC_SEQ_MUST_BEFILLED			= "businesslogic.document.seqmustbefilled";
	public static final String MESSAGE_ERROR_DOC_EMPTY_DOCUMENT_ID 			= "businesslogic.document.emptydocumentid";
	public static final String MESSAGE_ERROR_DOC_INVALID_PHONENIK			= "businesslogic.document.invalidphonenik";
	public static final String MESSAGE_ERROR_DOC_EMPTY_EMAIL				= "businesslogic.document.emptyemail";
	public static final String MESSAGE_ERROR_DOC_CERT_NOTFOUND				= "businesslogic.document.certnotfound";
	public static final String MESSAGE_ERROR_DOC_TENANT_NOT_MATCH			= "businesslogic.document.documentnotfoundintenant";
	public static final String MESSAGE_ERROR_DOC_INVALID_REF_NUMBER_LENGTH  = "businesslogic.document.invalidrefnumberlength";
	public static final String MESSAGE_ERROR_DOC_CURRENTLY_PROCESSED 		= "businesslogic.document.currentlyprocessed";
	
	public static final String MESSAGE_ERROR_EMAIL_SENDER	= "businesslogic.emailsender.sendemailerror";
	
	public static final String MESSAGE_ERROR_FEEDBACK_INVALIDVALUE	= "businesslogic.feedback.invalidvalue";
	public static final String MESSAGE_ERROR_FEEDBACK_INVALIDDOCID 	= "businesslogic.feedback.invaliddocid";
	
	public static final String MESSAGE_ERROR_PAYMENT_SIGN_TYPE_TENANT_NOT_FOUND		= "businesslogic.paymentsigntype.tenantnotfound";
	public static final String MESSAGE_ERROR_PAYMENT_SIGN_TYPE_TENANT_EMPTY_TENANT	= "businesslogic.paymentsigntype.emptytenantcode";
	public static final String MESSAGE_ERROR_PAYMENT_SIGN_TYPE_INVALID_ROLE			= "businesslogic.paymentsigntype.invalidrole";
	
	public static final String MESSAGE_ERROR_SDT_PARSE_FEE				= "businesslogic.stampduty.errorparsingfee";
	public static final String MESSAGE_ERROR_SDT_PARSE_QTY				= "businesslogic.stampduty.errorparsingqty";
	public static final String MESSAGE_ERROR_SDT_USER_CANNOT_CREATE		= "businesslogic.stampduty.usercannotcreate";
	public static final String MESSAGE_ERROR_SDT_VENDOR_TENANT_INVALID	= "businesslogic.stampduty.invalidvendortenant";
	public static final String MESSAGE_ERROR_SDT_VENDOR_CANNOT_CREATE	= "businesslogic.stampduty.vendorcannotcreate";
	
	public static final String MESSAGE_ERROR_SALDO_BALANCE_TYPE_ALREADY_EXIST 	= "businesslogic.saldo.balancetypealreadyexist";
	public static final String MESSAGE_ERROR_SALDO_BALANCE_TYPE_NOT_FOUND 		= "businesslogic.saldo.balancetypenotfound";
	public static final String MESSAGE_ERROR_SALDO_BALANCE_MUTATION_NOT_FOUND	= "businesslogic.saldo.balancemutationnotfound";
	public static final String MESSAGE_ERROR_SALDO_ID_BALANCE_MUTATION_INVALID	= "businesslogic.saldo.idbalancemutationinvalid";
	public static final String MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH			= "businesslogic.saldo.balancenotenough";
	public static final String MESSAGE_ERROR_SALDO_TENANT_NOT_EXIST				= "businesslogic.saldo.tenantnotexist";
	public static final String MESSAGE_ERROR_SALDO_PLEASE_CHOOSE_BALANCE_TYPE	= "businesslogic.saldo.pleasechoosebalancetype";
	public static final String MESSAGE_ERROR_SALDO_VENDOR_NOT_EXIST				= "businesslogic.saldo.vendornotexist";
	public static final String MESSAGE_ERROR_SALDO_BALANCE_NOT_CONFIGURED		= "businesslogic.saldo.balancenotconfigured";
	public static final String MESSAGE_ERROR_SALDO_TOPUP_DATE_NOT_EXIST			= "businesslogic.saldo.topupdatenotexist";
	
	public static final String MESSAGE_ERROR_OFFICE_NOT_EXIST = "businesslogic.usermanagement.officeisnotexist";
	public static final String MESSAGE_ERROR_BRANCH_NOT_EXIST = "businesslogic.document.branchnotfound";
	
	public static final String MESSAGE_ERROR_VENDOR_TYPE_CODE_EMPTY 		= "businesslogic.vendor.emptyvendortypecode";
	public static final String MESSAGE_ERROR_VENDOR_TYPE_CODE_INVALID 		= "businesslogic.vendor.invalidvendortypecode";
	public static final String MESSAGE_ERROR_VENDOR_TENANT_CODE_EMPTY 		= "businesslogic.vendor.emptytenantcode";
	public static final String MESSAGE_ERROR_VENDOR_TENANT_CODE_INVALID		= "businesslogic.vendor.invalidtenantcode";
	public static final String MESSAGE_ERROR_VENDOR_USER_TENANT_NOT_FOUND	= "businesslogic.vendor.usertenantnotfound";
	public static final String MESSAGE_ERROR_VENDOR_INVALID_VENDOR_TYPE		= "businesslogic.vendor.invalidvendortype";
	public static final String MESSAGE_ERROR_VENDOR_DEFAULT_NOT_FOUND		= "businesslogic.vendor.defaultvendornotfound";
	public static final String MESSAGE_ERROR_VENDOR_NOT_EXIST_OR_ACTIVE_IN_TENANT = "businesslogic.vendor.vendornotexistoractiveintenant";
	public static final String MESSAGE_ERROR_VENDOR_NOT_ACTIVE = "businesslogic.vendor.vendornotactive";
	public static final String MESSAGE_ERROR_VENDOR_NOT_OPERATING = "businesslogic.vendor.vendornotoperating";
	public static final String MESSAGE_ERROR_VENDOR_REGISTERED_USER_NOT_FOUND	= "businesslogic.vendor.registeredusernotfound";
	public static final String MESSAGE_ERROR_VENDOR_CAN_NOT_RESEND_ACT_LINK	= "businesslogic.vendor.vendorcannotresendactlink";
	public static final String MESSAGE_ERROR_VENDOR_ALREADY_REGISTERED		= "businesslogic.vendor.registeredinvendor";
	public static final String MESSAGE_ERROR_VENDOR_ALREADY_REGISTEREDV2		= "businesslogic.vendor.registereduserv2";
	public static final String MESSAGE_ERROR_VENDOR_UNHANDLED_REGIS_VENDOR = "businesslogic.vendor.unhandledregistrationvendor";
	public static final String MESSAGE_ERROR_VENDOR_VENDOR_CODE_INVALID = "businesslogic.vendor.vendorcodeinvalid";
	public static final String MESSAGE_ERROR_VENDOR_EMPTY_PSRE_LIST = "businesslogic.vendor.emptypsrelist";
	public static final String MESSAGE_ERROR_DEFAULT_VENDOR_NULL = "businesslogic.vendor.defaultvendornull";
	
	public static final String MESSAGE_ERROR_CHANGE_PW_MIN_CHAR_ERROR	= "businesslogic.changepassword.minchars";
	public static final String MESSAGE_ERROR_CHANGE_PW_WRONG_PASS		 = "businesslogic.changepassword.incorrectold";
	
	public static final String MESSAGE_ERROR_INV_LINK_PROCESS_ONLY_FOR_INV_BY_EMAIL = "businesslogic.invitationlink.processonlyforinvitationbyemail";
	public static final String MESSAGE_ERROR_INV_LINK_NOT_EXIST 					= "businesslogic.invitationlink.invitationlinknotexist";
	public static final String MESSAGE_ERROR_INV_LINK_CANNOT_SEND_OTP_TO_EMAIL 		= "businesslogic.invitationlink.cannotsendotptoemail";
	public static final String MESSAGE_ERROR_INV_LINK_CANNOT_VERIFY_OTP_TO_EMAIL 	= "businesslogic.invitationlink.cannotverifyotptoemail";
	public static final String MESSAGE_ERROR_INV_LINK_INACTIVE_LINK 				= "businesslogic.invitationlink.inactivelink";
	public static final String MESSAGE_ERROR_INV_LINK_INVALID 						= "businesslogic.invitationlink.invalidlink";
	public static final String MESSAGE_ERROR_INV_LINK_INVALID_EMAIL					= "businesslogic.invitationlink.invalidemail";
	public static final String MESSAGE_ERROR_INV_LINK_INVALID_PHONE					= "businesslogic.invitationlink.invalidphoneno";
	public static final String MESSAGE_ERROR_INV_LINK_NOT_EXIST_WITH_DATA	        = "businesslogic.invitationlink.invitationlinknotexistwithdata";
	public static final String MESSAGE_ERROR_INV_LINK_EXISTING_PHONE_OTHER_LINK		= "businesslogic.invitationlink.existingphoneotherlink";
	public static final String MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL_OTHER_LINK		= "businesslogic.invitationlink.existingemailotherlink";
	public static final String MESSAGE_ERROR_INV_LINK_EXISTING_ID_NO_OTHER_LINK		= "businesslogic.invitationlink.existingidnootherlink";
	public static final String MESSAGE_ERROR_INV_LINK_EXISTING_EMAIL				= "businesslogic.invitationlink.existingemail";
	public static final String MESSAGE_ERROR_INV_LINK_EMAIL_PHONE_USED_INNIK		= "businesslogic.invitationlink.emailphoneusedinnik";
	public static final String MESSAGE_ERROR_INV_LINK_EMAIL_USED_INNIK				= "businesslogic.invitationlink.emailusedinnik";
	public static final String MESSAGE_ERROR_INV_LINK_INVITATION_SENT				= "businesslogic.invitationlink.invitationsent";
	public static final String MESSAGE_ERROR_PHONE_NUMBER_NULL 						= "businesslogic.invitationlink.phonenull";
	
	public static final String MESSAGE_ERROR_REGISTER_USER_REGISTERED = "businesslogic.register.alreadyregistered";
	public static final String MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE = "businesslogic.register.nikusedbyotherphone";
	public static final String MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_EMAIL = "businesslogic.register.nikusedbyotheremail";
	public static final String MESSAGE_ERROR_REGISTER_NIK_USED_BY_OTHER_PHONE_EMAIL = "businesslogic.register.nikusedbyotherphoneemail";
	public static final String MESSAGE_ERROR_REGISTER_PHONE_USED_BY_OTHER_NIK = "businesslogic.register.phoneusedbyothernik";
	public static final String MESSAGE_ERROR_REGISTER_EMAIL_USED_BY_OTHER_NIK = "businesslogic.register.emailusedbyothernik";
	public static final String MESSAGE_ERROR_REGISTER_PHONE_DOES_NOT_BELONG_TO_NIK = "businesslogic.register.phonenotbelongtonik";
	public static final String MESSAGE_ERROR_REGISTER_EMAIL_DOES_NOT_BELONG_TO_NIK = "businesslogic.register.emailnotbelongtonik";
	public static final String MESSAGE_ERROR_REGISTER_PHONE_EMAIL_DOES_NOT_BELONG_TO_NIK = "businesslogic.register.phoneemailnotbelongtonik";
	
	public static final String MESSAGE_ERROR_TENANT_INVALID_THRESHOLD_BALANCE	= "businesslogic.tenant.invalidthresholdbalance";
	public static final String MESSAGE_TEMPLATE_WITHOUT_ACTIVE_DURATION = "businesslogic.tenant.msgtemplatewithotpactiveduration";
	public static final String MESSAGE_ERROR_TENANT_NOT_FOUND					= "businesslogic.tenant.tenantnotfound";
	public static final String MESSAGE_ERROR_TENANT_CODE_EMPTY					= "businesslogic.tenant.tenantcodeempty";
	public static final String MESSAGE_ERROR_TENANT_INVALID_CALLBACK_URL		= "businesslogic.tenant.invalidcallbackurl";
	
	public static final String MESSAGE_ERROR_EMETERAI_FAILED_GETDOCUMENT		= "businesslogic.emeterai.failedtogetossdocument";
	
	public static final String MESSAGE_ERROR_JOB_TYPE_EMPTY 		= "businesslogic.job.emptyjobtype";
	public static final String MESSAGE_ERROR_ID_JOB_RESULT_EMPTY 		= "businesslogic.job.emptyidjobresult";
	
	public static final String MESSAGE_ERROR_TEKENAJA_WAIT_FOR_SIGN	= "businesslogic.tekenaja.waitforsign";
	
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_SENT_OTP_FAILED = "businesslogic.tekenaja.hashsign.sentotpfailed";
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_USER_HAS_NOT_REGISTERED = "businesslogic.tekenaja.hashsign.userhasnotregistered";
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_FAILED_SIGN = "businesslogic.tekenaja.hashsign.failedsign";
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_INVALID_OTP = "businesslogic.tekenaja.hashsign.invalidotp";
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_OTP_EXPIRED = "businesslogic.tekenaja.hashsign.otpexpired";
	public static final String MESSAGE_ERROR_TEKENAJA_HASHSIGN_SYSTEM_FAILURE = "businesslogic.tekenaja.hashsign.systemfailure";
	
	public static final String MESSAGE_ERROR_DIGISIGN_CONNECT_TIMED_OUT	= "businesslogic.digisign.connectiontimeout";
	public static final String MESSAGE_ERROR_DIGISIGN_CONNECT_READ_TIMED_OUT	= "businesslogic.digisign.readtimeout";

	public static final String MESSAGE_ERROR_VIDA_SELFPHOTO_NOT_FOUND = "businesslogic.vida.selfphotonotfound";
	
	public static final String MESSAGE_ERROR_PRIVY_IDNOTFOUND = "businesslogic.privy.prifyidnotfound";
	
	public static final String MESSAGE_ERROR_EMBED_MSG_INVALID	= "businesslogic.embedmsg.invalidmsg";
	public static final String MESSAGE_ERROR_EMBED_MSG_EMPTY	= "businesslogic.embedmsg.emptymsg";
	public static final String MESSAGE_ERROR_EMBED_INVALID_ENCRYPTED = "businesslogic.embedmsg.invalidencryptedobject";
	
	public static final String MESSAGE_ERROR_USERVAL_EMAILNOTFOUND 		= "businesslogic.userval.emailnotfound";
	public static final String MESSAGE_ERROR_USERVAL_PHONENOTFOUND		= "businesslogic.userval.phonenotfound";
	public static final String MESSAGE_ERROR_USERVAL_EMAILUSED_BY_MANY	= "businesslogic.userval.emailusedbymany";
	
	public static final String MESSAGE_SUCCESS_COMMON_DELETE = "success.common.delete";
	public static final String MESSAGE_SUCCESS_COMMON_SAVE= "success.common.save";
	
	public static final String MESSAGE_AUTOSIGN_FILENAME_EMPTY = "businesslogic.autosign.emptyfilename";
	public static final String MESSAGE_AUTOSIGN_BASE64_EMPTY = "businesslogic.autosign.emptybase64";
	public static final String MESSAGE_AUTOSIGN_EXECUTE_TIME_EMPTY = "businesslogic.autosign.emptyexecutiontime";
	public static final String MESSAGE_AUTOSIGN_INVALID_DATE_RANGE = "businesslogic.autosign.invaliddaterange";
	public static final String MESSAGE_AUTOSIGN_INVALID_STATUS = "businesslogic.autosign.invalidstatusimport";
	public static final String MESSAGE_AUTOSIGN_EMPTY_TEMPLATE_FILE = "businesslogic.autosign.emptytemplatefile";
	
	public static final String MESSAGE_MESSAGE_DELIVERY_REPORT_INVALID_DATE = "businesslogic.document.invaliddaterange";

	public static final String GOOGLE_API_STATICMAPS = "google.api.staticmaps";
	
	public static final String GOOGLE_API_FIREBASE = "google.api.firebase";
	
	
	public static final String TYPE_OF_PROJECT = "type.project";
	
	public static final String FILENAME_PDF = "fileName.pdf";
	
	public static final String FILENAME_IMG = "fileName.img";

	
	public static final String MULTITENANT_IMAGE_URL = "multitenant.image.url";
	
	public static final String HTTP_HEADER_TENANT = "Tenant";
		
	public static final String KEY_KONVERGEN="key.konvergen";
	
	public static final String SECRET_KEY = "secret.genkey";
	public static final String SECRET_IV = "secret.iv";

	public static final String MESSAGE_TENANT_SETTINGS_NOT_FOUND = "businesslogic.tenantsettings.tenantsettingsnotfound";
	public static final String MESSAGE_TENANT_SETTINGS_VALUE_EMPTY = "businesslogic.tenantsettings.settingvalueempty";

	
	public static final String GENERAL_SETTING_NOT_FOUND = "businesslogic.generalsetting.gsinexist";
}
