package com.adins.esign.businesslogic.impl;

import java.io.IOException;
import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.mail.MessagingException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.am.model.AmGeneralsetting;
import com.adins.am.model.AmMemberofrole;
import com.adins.am.model.AmMsrole;
import com.adins.am.model.AmMsuser;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.CommonLogic;
import com.adins.esign.businesslogic.api.DocumentLogic;
import com.adins.esign.businesslogic.api.EmailLogic;
import com.adins.esign.businesslogic.api.EmailSenderLogic;
import com.adins.esign.businesslogic.api.MessageDeliveryReportLogic;
import com.adins.esign.businesslogic.api.MessageTemplateLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SaldoLogic;
import com.adins.esign.businesslogic.api.SendDocumentLogic;
import com.adins.esign.businesslogic.api.SmsLogic;
import com.adins.esign.businesslogic.api.TenantLogic;
import com.adins.esign.businesslogic.api.TenantSettingsLogic;
import com.adins.esign.businesslogic.api.UserLogic;
import com.adins.esign.businesslogic.api.VendorLogic;
import com.adins.esign.businesslogic.api.interfacing.DigisignLogic;
import com.adins.esign.businesslogic.api.interfacing.JatisSmsLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.businesslogic.api.interfacing.TekenAjaLogic;
import com.adins.esign.businesslogic.api.interfacing.VidaLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppHalosisLogic;
import com.adins.esign.businesslogic.api.interfacing.WhatsAppLogic;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.TekenAjaConstant;
import com.adins.esign.constants.enums.NotificationSendingPoint;
import com.adins.esign.constants.enums.NotificationType;
import com.adins.esign.model.MsBusinessLine;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsMsgTemplate;
import com.adins.esign.model.MsNotificationtypeoftenant;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsRegion;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsUseroftenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrErrorHistory;
import com.adins.esign.model.TrErrorHistoryUserDetail;
import com.adins.esign.model.TrInvitationLink;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.EmailInformationBean;
import com.adins.esign.model.custom.PersonalDataBean;
import com.adins.esign.model.custom.SendDocFullApiRequestBean;
import com.adins.esign.model.custom.SendDocFullApiResponseBean;
import com.adins.esign.model.custom.SendDocFullApiSignerBean;
import com.adins.esign.model.custom.SignLocationBean;
import com.adins.esign.model.custom.SignLocationFullApiBean;
import com.adins.esign.model.custom.SignatureDetailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.TknajRegisterCekResponse;
import com.adins.esign.model.custom.TknajUplDocResponse;
import com.adins.esign.model.custom.halosis.HalosisSendWhatsAppRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsRequestBean;
import com.adins.esign.model.custom.jatis.JatisSmsResponse;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.BalanceValidatorLogic;
import com.adins.esign.validatorlogic.api.CommonValidatorLogic;
import com.adins.esign.validatorlogic.api.DocumentValidatorLogic;
import com.adins.esign.validatorlogic.api.TenantValidatorLogic;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.validatorlogic.api.VendorValidatorLogic;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.DocumentConfinsResponse;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.SendDocFullApiRequest;
import com.adins.esign.webservices.model.SendDocFullApiResponse;
import com.adins.esign.webservices.model.SendSmsResponse;
import com.adins.esign.webservices.model.SendSmsValueFirstRequestBean;
import com.adins.esign.webservices.model.SendWhatsAppRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.adins.esign.webservices.model.vida.PoaRequest;
import com.adins.esign.webservices.model.vida.PoaResponse;
import com.adins.exceptions.DigisignException;
import com.adins.exceptions.DocumentException;
import com.adins.exceptions.DuplicateRequestException;
import com.adins.exceptions.ParameterException;
import com.adins.exceptions.ParameterException.ReasonParam;
import com.adins.exceptions.SaldoException;
import com.adins.exceptions.StatusCode;
import com.adins.exceptions.TekenajaException;
import com.adins.exceptions.TenantException;
import com.adins.exceptions.TenantException.ReasonTenant;
import com.adins.exceptions.UserException;
import com.adins.exceptions.DocumentException.ReasonDocument;
import com.adins.exceptions.SaldoException.ReasonSaldo;
import com.adins.exceptions.SendNotificationException;
import com.adins.exceptions.SendNotificationException.ReasonSendNotif;
import com.adins.exceptions.UserException.ReasonUser;
import com.adins.exceptions.VendorException;
import com.adins.exceptions.VendorException.ReasonVendor;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.password.PasswordHash;
import com.google.gson.Gson;

@Transactional
@Component
public class GenericSendDocumentLogic extends BaseLogic implements SendDocumentLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericSendDocumentLogic.class);
	
	@Value("${esign.sign.uri}") private String linkTtdEsign;
	@Value("${spring.mail.username}") private String fromEmailAddr;
	@Value("${esign.regex.phone}") private String regexPhone;
	@Value("${esign.regex.email}") private String regexEmail;
	    
	@Autowired private CloudStorageLogic cloudStorageLogic;
	@Autowired private CommonLogic commonLogic;
	@Autowired private DigisignLogic digisignLogic;
	@Autowired private DocumentLogic documentLogic;
	@Autowired private EmailLogic emailLogic;
	@Autowired private EmailSenderLogic emailSenderLogic;
	@Autowired private MessageTemplateLogic msgTemplateLogic;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private SaldoLogic saldoLogic;
	@Autowired private SmsLogic smsLogic;
	@Autowired private TekenAjaLogic tekenajaLogic;
	@Autowired private TenantLogic tenantLogic;
	@Autowired private UserLogic userLogic;
	@Autowired private VendorLogic vendorLogic;
	@Autowired private VidaLogic vidaLogic;
	@Autowired private WhatsAppLogic whatsAppLogic;
	@Autowired private JatisSmsLogic jatisSmsLogic;
	@Autowired private PrivyGeneralLogic privyGeneralLogic;
	@Autowired private WhatsAppHalosisLogic whatsAppHalosisLogic;
	@Autowired private TenantSettingsLogic tenantSettingsLogic;
	@Autowired private MessageDeliveryReportLogic messageDeliveryReportLogic;
	@Autowired private TenantValidatorLogic tenantValidatorLogic;
	@Autowired private UserValidatorLogic userValidatorLogic;
	@Autowired private VendorValidatorLogic vendorValidatorLogic; 
	@Autowired private BalanceValidatorLogic balanceValidatorLogic;
	@Autowired private CommonValidatorLogic commonValidatorLogic;
	@Autowired private Gson gson;
	
	private static final String MAP_KEY_EMAIL 		= "email";
    private static final String MAP_KEY_FULLNAME	= "fullname";
    private static final String MAP_KEY_EMAIL_SENT 	= "emailSent";
    private static final String MAP_KEY_PHONE 		= "phone";
    private static final String MAP_KEY_TENANT 		= "tenant";
    private static final String MAP_KEY_SIGN_ACTION = "signAction";
    private static final String MAP_KEY_PASSWORD	= "password";
    private static final String MAP_KEY_AUDIT_TRAIL	= "auditTrail";
    private static final String MAP_KEY_NIK			= "nik";
    
    private static final String RESEND_SIGN_REQ_SMS_NOTES_FORMAT = "%s : Send SMS Sign Request";
    private static final String RESEND_SIGN_REQ_WA_NOTES_FORMAT = "%s : Send WhatsApp Sign Request";
    
    private static final String MSG_ERRORSEND	= "businesslogic.document.errorsend";
    private static final String MSG_MAXSIGNEXCEEDED	= "businesslogic.document.maxsignexceeded";
    private static final String MSG_DOC_CANNOT_AUTOSIGN_WHEN_SEQ = "businesslogic.document.cannotautosignwhensequential";
    
    private static final String TEKEN_AJA 		= "TekenAja";
    private static final String ERROR 			= " error ";
    private static final String CONST_FAIL_POA_VIDA = "Fail PoA Vida : ";
    private static final String DUMMY_TRX_NO		= "XXX";
    private static final String SEND_WA_TO_NOTE		= "Sending WhatsApp to ";
    
	@Override
	public DocumentConfinsResponse sendDocument(String tenantCode, String vendorCode, DocumentConfinsRequestBean[] documentConfinsRequest, String apiKey, AuditContext audit) throws Exception {		
		DocumentConfinsResponse response = new DocumentConfinsResponse();
		Status status = new Status();
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(tenantCode, true, audit);
		String[] split = apiKey.split("@");

		MsVendor vendor;
		if (StringUtils.isNotBlank(vendorCode)) {
			vendor = vendorValidatorLogic.validateGetVendorByStatusAndTenant(vendorCode, tenantCode, audit);
		} else {
			vendor = this.checkRequestTemplateAndVendor(documentConfinsRequest[0].getDocumentTemplateCode(), tenantCode, audit);
		}
		
		//RefNo
		if (StringUtils.length(documentConfinsRequest[0].getReferenceNo()) > 50) {
			throw new ParameterException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_REF_NUMBER_LENGTH, null, this.retrieveLocaleAudit(audit)), ReasonParam.INVALID_LENGTH);
		}
		
		try {
			checkTenantApiKey(split[1], tenant, audit);
			this.checkRequestArray(documentConfinsRequest, tenantCode, vendor.getVendorCode(), audit);
			if ("1".equals(tenant.getUserMustRegister()) && "1".equals(vendor.getUserMustRegister())) {
				//isi no telp udah ga sesai sama isi di postman
				this.rejectUnregisteredSigner(documentConfinsRequest, vendor, audit);
			}
			this.checkDuplicateRefNumber(documentConfinsRequest[0].getReferenceNo(), tenant, audit);
		} catch (Exception e) {
			String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
			this.insertErrorHistorySendDoc(documentConfinsRequest, msg, tenant, vendor, audit);
			
			throw e;
		}
		
		// CONFINS masih kirim null
		MsOffice office = null;
		MsBusinessLine businessLine = null;
		MsRegion region = null;
		AmMsuser user = null;
		Map<String, Object> passwords = new HashMap<>();
		
		Set<Map<String, Object>> userEmailSet = new HashSet<>();
		List<Map<String, Object>> userEmailList;
		
		int k = 0;
		Map<String, Object> checked = new HashMap<>();
		List<String> emailListed = new ArrayList<>();
		for (DocumentConfinsRequestBean reqBean : documentConfinsRequest) {
			validateSignerRegistration(reqBean.getSigner(), vendor, audit);
			for(SignerBean bean : reqBean.getSigner()) {
				this.deactivateInvLink(bean.getEmail(), bean.getUserPhone(), bean.getIdNo(), vendor.getVendorCode(), audit);
				
				try {
					// membuat email bagi yang belum punya dan menambahkan setting email service
					// throws exception kalau email tidak ada dan email service tenant tidak aktif
					this.emailCreatorAndEmailServiceSetting(bean, checked, tenant, vendor, emailListed, userEmailSet, audit);
				} catch (Exception e) {
					String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
					this.insertErrorHistorySendDoc(documentConfinsRequest, msg, tenant, vendor, audit);
					
					throw e;
				}
			}
			
			String[] pass;
			try {
				region = checkRegionSendDoc(reqBean.getRegionCode(), reqBean.getRegionName(), tenant, audit);
				office = checkOfficeSendDoc(reqBean.getOfficeCode(), reqBean.getOfficeName(), tenant, region, audit);
				businessLine = checkBusinessLineSendDoc(reqBean.getBusinessLineCode(), reqBean.getBusinessLineName(), tenant, audit);
				pass = checkIfUserIsRegistered(reqBean.getSigner(), tenant, vendor, office, reqBean.getDocumentTemplateCode(), audit);
				passwords.put(String.valueOf(k),pass);
				// untuk mengubah no telp sesuai dengan yang sudah tersimpan di db
				this.checkSignerPhoneNumber(userEmailSet, reqBean.getSigner());
			} catch (UserException e) {
				String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
				this.insertErrorHistorySendDoc(documentConfinsRequest, msg, tenant, vendor, audit);
				LOG.error(GlobalVal.STACK_TRACE, (Object[]) e.getStackTrace());
				
				status.setCode(StatusCode.UNKNOWN);
				status.setMessage(e.getLocalizedMessage());
				response.setStatus(status);
				return response;
			}
			
			for(SignerBean bean : reqBean.getSigner()) {
				if(bean.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_CUST)) {
					user = userValidatorLogic.validateGetUserByEmailv2(bean.getLoginId(), false, audit);
				} 
			}
			
			k++;
		}
		
		userEmailList = new ArrayList<>(userEmailSet);
		
		try {
			TrDocumentH docH = documentLogic.insertDocumentH(documentConfinsRequest[0].getReferenceNo(), user, office, tenant, 
						documentConfinsRequest.length, 0, GlobalVal.DOC_TYPE_AGREEMENT, 
						documentConfinsRequest[0].getSuccessURL(), documentConfinsRequest[0].getUploadURL(), businessLine, audit);
			
			int j = 0;
			String prevFile = null;
			String[] documentIds = new String[documentConfinsRequest.length];
			for (DocumentConfinsRequestBean docReq : documentConfinsRequest) {
				// Set manual karena confins tidak mengirim login id dan tidak bisa double serializable
				if (null == prevFile) {
					prevFile = docReq.getDocumentFile();
				} else {
					if (isSameFile(prevFile, docReq.getDocumentFile())) {
						throw new DuplicateRequestException(this.messageSource.getMessage("businesslogic.document.samefile", null, 
								this.retrieveLocaleAudit(audit)));
					}
				}
				
				checkRequestSignerEmail(docReq, audit);
	
				documentIds[j] = sendDocumentOneByOne(tenant, vendor, docReq, docH, (String[]) passwords.get(String.valueOf(j)), userEmailList, audit);
				
				j++;
			}	
			
			response.setDocumentId(documentIds);
		} catch (Exception e) {
			String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
			this.insertErrorHistorySendDoc(documentConfinsRequest, msg, tenant, vendor, audit);
			
			throw e;
		}
		
		return response;
	}

	private void validateSignerRegistration(SignerBean[] signers, MsVendor vendor, AuditContext audit) {
		for (SignerBean signer : signers) {
			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdNoAndVendorCode(signer.getIdNo(), vendor.getVendorCode());

			if ("at".equalsIgnoreCase(signer.getSignAction())) {
				validateAutosignSigner(signer, vru, audit);
			} else {
				validateManualSigner(signer, vru, audit);
			}
		}
	}

	private void validateAutosignSigner(SignerBean signer, MsVendorRegisteredUser vru, AuditContext audit) {
		if (StringUtils.isBlank(vru.getPoaId()) ||
			StringUtils.isBlank(vru.getVendorUserAutosignCvv()) ||
			StringUtils.isBlank(vru.getVendorUserAutosignKey()) ||
			vru.getCertPoaExpiredDate() == null) {
			throw new UserException(
				messageSource.getMessage("businesslogic.document.poacert.incomplete", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)),
				ReasonUser.POA_CERT_INCOMPLETE
			);
		}
		if (vru.getCertPoaExpiredDate().before(new Date())) {
			throw new UserException(
				messageSource.getMessage("businesslogic.document.poacert.expired", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)),
				ReasonUser.POA_CERT_EXPIRED
			);
		}
	}

	private void validateManualSigner(SignerBean signer, MsVendorRegisteredUser vru, AuditContext audit) {
		if (StringUtils.isBlank(vru.getVendorRegistrationId())) {
			throw new UserException(
				messageSource.getMessage("businesslogic.document.vendorregistrationidempty", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)),
				ReasonUser.VENDOR_REGISTRATION_ID_EMPTY
			);
		}
		if (!"1".equals(vru.getIsActive())) {
			throw new UserException(
				messageSource.getMessage("businesslogic.document.notactive", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)),
				ReasonUser.VENDOR_USER_NOT_ACTIVE
			);
		}
		if (vru.getCertExpiredDate() == null) {
			throw new UserException(
				messageSource.getMessage("businesslogic.document.certdate.notfound", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)),
				ReasonUser.CERT_EXPIRED_DATE_EMPTY
			);
		}
	}
	
	private void insertAuditTrailHeader(List<Map<String, Object>> userEmailList, String email, boolean isSuccess) {
		for (Map<String, Object> user : userEmailList) {
			String emailFromList = (String) user.get(MAP_KEY_EMAIL);
			if (email.equals(emailFromList)) {
				TrSigningProcessAuditTrail auditTrail = (TrSigningProcessAuditTrail) user.get(MAP_KEY_AUDIT_TRAIL);
				if (null == auditTrail.getAmMsUser()) {
					auditTrail.setAmMsUser(daoFactory.getUserDao().getUserByIdNo((String) user.get(MAP_KEY_NIK)));
				}
				
				auditTrail.setResultStatus(isSuccess ? "1" : "0");
				
				daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrail(auditTrail);
			}
		}
	}
	
	private MsVendor checkRequestTemplateAndVendor(String templateCode, String tenantCode, AuditContext audit) {
		if (StringUtils.isBlank(templateCode)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {GlobalVal.CONST_DOCUMENT_TEMPLATE_CODE}, this.retrieveLocaleAudit(audit)), 
					ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		
		MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(
				templateCode, tenantCode);
		
		if (null == docTemplate) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new Object[] {templateCode}, this.retrieveLocaleAudit(audit)), 
						ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		
		MsVendor vendor = docTemplate.getMsVendor();
		
		if (null == vendor || !"1".equals(vendor.getIsOperating())) {
			vendor = vendorValidatorLogic.validateGetOperatingDefaultVendor(tenantCode, false, audit);
		}
		
		return vendor;
	}
	
	private MsVendor checkRequestTemplateAndVendorFullApi(String templateCode, String tenantCode, String psreCode, AuditContext audit) {
		
		MsVendor vendor;
		if (StringUtils.isNotBlank(psreCode)) {
			vendor = vendorValidatorLogic.validateVendorOfTenant(psreCode, tenantCode, true, audit);

			if(!"1".equals(vendor.getIsOperating())) {
				throw new VendorException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_VENDOR_NOT_OPERATING,
						new String[] {vendor.getVendorCode()}, this.retrieveLocaleAudit(audit)), ReasonVendor.VENDOR_NOT_OPERATING);
			}
			
			return vendor;
		}
		
		if (StringUtils.isNotBlank(templateCode)) {
			MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(
					templateCode, tenantCode);
			
			if (null == docTemplate) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
						new Object[] {templateCode}, this.retrieveLocaleAudit(audit)), 
							ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
			}
			
			vendor = docTemplate.getMsVendor();
			
			if (null == vendor || !"1".equals(vendor.getIsOperating())) {
				vendor = vendorValidatorLogic.validateGetOperatingDefaultVendor(tenantCode, false, audit);
			}
			
			return vendor;
		} 
		
		vendor = vendorValidatorLogic.validateGetOperatingDefaultVendor(tenantCode, false, audit);
		
		return vendor;
		
	}
	
	private void checkRequestArray(DocumentConfinsRequestBean[] documentConfinsRequest, String tenantCode, String vendorCode, AuditContext audit) {
		Map<String, String> phones = new HashMap<>();
		Map<String, String> emails = new HashMap<>();
		
		for (DocumentConfinsRequestBean reqBean : documentConfinsRequest) {
			this.checkRequest(reqBean, tenantCode, vendorCode, phones, emails, audit);
		}
	}
	
	private void checkRequest(DocumentConfinsRequestBean documentConfinsRequest, String tenantCode, String vendorCode, Map<String, String> phones, Map<String, String> emails, AuditContext audit) {
		String messageValidation = "";
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] { "DocumentConfinsRequest" }, audit);
		commonValidatorLogic.validateNotNull(documentConfinsRequest, messageValidation, StatusCode.UNKNOWN);
		
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] { "referenceNo" }, audit);
		commonValidatorLogic.validateNotNull(documentConfinsRequest.getReferenceNo(), messageValidation, StatusCode.REFERENCE_NO_NOT_EXISTS);
		
		
		if (StringUtils.isBlank(documentConfinsRequest.getDocumentTemplateCode())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {GlobalVal.CONST_DOCUMENT_TEMPLATE_CODE}, this.retrieveLocaleAudit(audit)), 
					ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		else if (null == daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(
				documentConfinsRequest.getDocumentTemplateCode(), tenantCode)) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new Object[] {documentConfinsRequest.getDocumentTemplateCode()}, this.retrieveLocaleAudit(audit)), 
						ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		}
		
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] { "documentFile" }, audit);
		commonValidatorLogic.validateNotNull(documentConfinsRequest.getDocumentFile(), messageValidation, StatusCode.DOCUMENT_FILE_NOT_EXISTS);
		
		
		if (null == documentConfinsRequest.getSigner() || documentConfinsRequest.getSigner().length == 0) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {"signer"}, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(tenantCode, true, audit);
		List<String> signers = getDocTemplateSignerTypes(documentConfinsRequest.getDocumentTemplateCode(), tenant.getIdMsTenant());
		List<String> availSigners = new ArrayList<>();
		
		for (SignerBean signerBean : documentConfinsRequest.getSigner()) {
			String signerType = checkRequestSigners(signerBean, documentConfinsRequest, tenantCode, vendorCode, phones, emails, audit);
			availSigners.add(signerType);
			
			if (GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(signerBean.getSignAction()) && "1".equals(documentConfinsRequest.getIsSequence())) {
				throw new DocumentException(messageSource.getMessage(MSG_DOC_CANNOT_AUTOSIGN_WHEN_SEQ, null, this.retrieveLocaleAudit(audit)),
						ReasonDocument.CANNOT_AUTOSIGN);
			}
			
			if (GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(signerBean.getSignAction()) && GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendorCode)) {
				throw new DocumentException(messageSource.getMessage("businesslogic.document.cannotautosignwithvendor", new Object[] {vendorCode}, this.retrieveLocaleAudit(audit)),
						ReasonDocument.CANNOT_AUTOSIGN);
			}
		}
		
		for(String signerType : signers) {
			if (!availSigners.contains(signerType)) {
				throw new DocumentException(messageSource.getMessage("businesslogic.document.mustsendsigner", 
						new Object[] {signerType, documentConfinsRequest.getDocumentTemplateCode()},  this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
			}
		}
	}
	
	private String checkRequestSigners(SignerBean signerBean, DocumentConfinsRequestBean documentConfinsRequest, String tenantCode, String vendorCode, Map<String, String> phones, Map<String, String> emails, AuditContext audit) {
		if (StringUtils.isBlank(signerBean.getUserPhone())) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST, new Object[] {"tlp"}, this.retrieveLocaleAudit(audit)), ReasonDocument.ERROR_EXIST);
		}
		this.validateSendDocumentNik(signerBean.getIdNo(), signerBean.getEmail(), vendorCode, audit);
		this.checkEmailAndPhoneInRequest(phones, emails, signerBean.getUserPhone(), signerBean.getEmail(), signerBean.getIdNo(), audit);
		if (StringUtils.isBlank(signerBean.getSignAction())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {MAP_KEY_SIGN_ACTION}, this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_ACTION_NOT_EXISTS);
		}
		if (StringUtils.isBlank(signerBean.getSignerType())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {"signerType"}, this.retrieveLocaleAudit(audit)), ReasonDocument.SIGNER_TYPE_NOT_EXISTS);
		}
		
		List<String> signerTypes = daoFactory.getLovDao().getListofCodeByLovGroup(GlobalVal.LOV_GROUP_SIGNER_TYPE);
		if (!signerTypes.contains(StringUtils.upperCase(signerBean.getSignerType()))) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.invalidsignertype",
					new Object[] {signerBean.getSignerType()}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(signerBean.getUserName())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {"userName"}, this.retrieveLocaleAudit(audit)), ReasonDocument.NAME_NOT_EXISTS);
		}
		if (StringUtils.equals("-", StringUtils.trim(signerBean.getEmail()))) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
					new Object[] {signerBean.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		if (StringUtils.equals("-", StringUtils.trim(signerBean.getUserPhone()))) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
					new Object[] {signerBean.getUserPhone()}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		if(!(signerBean.getSignerType().equals("MF") && signerBean.getSignAction().equals("at"))) {
			this.checkIdNoPhoneAndEmail(signerBean.getIdNo(), signerBean.getUserPhone(), signerBean.getEmail(), vendorCode, audit);
		}
		
		return signerBean.getSignerType();
	}
	
	private void validateSendDocumentNik(String idNo, String email, String vendorCode, AuditContext audit) {
		if (StringUtils.isBlank(idNo)) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST, new Object[] {"idKtp"}, this.retrieveLocaleAudit(audit)), ReasonDocument.ERROR_EXIST);
		}
		
		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(idNo);			
		if (null == nikUser) {
			return;
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(nikUser.getLoginId(), vendorCode);
		if (null == vendorUser) {
			return;
		}
		
		// ESH-1016 Case 1
		// User yang sama boleh memakai email dan/atau no telp yang sama untuk vendor berbeda
//		if ("0".equals(vendorUser.getEmailService()) && StringUtils.isNotBlank(signerBean.getEmail())) {
//			MsVendorRegisteredUser vruEmail = daoFactory.getVendorDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(signerBean.getEmail(), vendorCode);
//			boolean isSameUser = null != vruEmail && vendorUser.getSignerRegisteredEmail().equals(vruEmail.getSignerRegisteredEmail());
//			if (!isSameUser) {
//				signerBean.setEmail(vendorUser.getSignerRegisteredEmail());
//				signerBean.setEmailService(vendorUser.getEmailService());
//			}
//		}
		
		// ESH-1016 Case 5
		if ("1".equals(vendorUser.getEmailService()) && StringUtils.isNotBlank(email)) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.nikdoesnothaveemail",
					new String[] {idNo}, retrieveLocaleAudit(audit)), ReasonDocument.INVALID_SEND_DOCUMENT_SIGNER);
		}
			
		// ESH-1016 Case 4
		if ("0".equals(vendorUser.getEmailService()) && StringUtils.isBlank(email)) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.emailmandatory",
					new String[] {idNo}, retrieveLocaleAudit(audit)), ReasonDocument.INVALID_SEND_DOCUMENT_SIGNER);
		}
		
		// ESH-1016 Case 2, 3
//		if ("1".equals(vendorUser.getEmailService()) && "1".equals(vendorUser.getIsActive())) {
//			signerBean.setUserPhone(personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea()));	
//		}
	}
	
	private void validateSendDocumentNikExternal(String idNo, String email, String vendorCode, AuditContext audit) {
		if (StringUtils.isBlank(idNo)) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST, new Object[] {"idKtp"}, this.retrieveLocaleAudit(audit)), ReasonDocument.ERROR_EXIST);
		}
		
		AmMsuser nikUser = daoFactory.getUserDao().getUserByIdNo(idNo);			
		if (null == nikUser) {
			return;
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByLoginIdAndVendorCode(nikUser.getLoginId(), vendorCode);
		if (null == vendorUser) {
			return;
		}
		
		// ESH-1016 Case 1
		// User yang sama boleh memakai email dan/atau no telp yang sama untuk vendor berbeda
//		if ("0".equals(vendorUser.getEmailService()) && StringUtils.isNotBlank(signerBean.getEmail())) {
//			MsVendorRegisteredUser vruEmail = daoFactory.getVendorDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(signerBean.getEmail(), vendorCode);
//			boolean isSameUser = null != vruEmail && vendorUser.getSignerRegisteredEmail().equals(vruEmail.getSignerRegisteredEmail());
//			if (!isSameUser) {
//				signerBean.setEmail(vendorUser.getSignerRegisteredEmail());
//				signerBean.setEmailService(vendorUser.getEmailService());
//			}
//		}
		
		// ESH-1016 Case 5
		if ("1".equals(vendorUser.getEmailService()) && StringUtils.isNotBlank(email) &&  !vendorUser.getSignerRegisteredEmail().equalsIgnoreCase(email)) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.nikdoesnothaveemailext",
					new String[] {idNo}, retrieveLocaleAudit(audit)), ReasonDocument.INVALID_SEND_DOCUMENT_SIGNER);
		}
			
		// ESH-1016 Case 4
		if ("0".equals(vendorUser.getEmailService()) && StringUtils.isBlank(email)) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.emailmandatory",
					new String[] {idNo}, retrieveLocaleAudit(audit)), ReasonDocument.INVALID_SEND_DOCUMENT_SIGNER);
		}
		
		// ESH-1016 Case 2, 3
//		if ("1".equals(vendorUser.getEmailService()) && "1".equals(vendorUser.getIsActive())) {
//			signerBean.setUserPhone(personalDataEncLogic.decryptToString(vendorUser.getPhoneBytea()));	
//		}
	}
	
	private void checkEmailAndPhoneInRequest(Map<String, String> phones, Map<String, String> emails, String phone, String email, String idNo, AuditContext audit) {
		if (phones.containsKey(phone) && !phones.get(phone).equals(idNo)) {
			throw new UserException(messageSource.getMessage("businesslogic.document.samephoneinrequest", 
					new Object[] {phone}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
		} else if (!phones.containsKey(phone) && StringUtils.isNotBlank(phone)) {
			phones.put(phone,idNo);
		}
		
		if (emails.containsKey(email) && !emails.get(email).equals(idNo)) {
			throw new UserException(messageSource.getMessage("businesslogic.document.sameemailinrequest", 
					new Object[] {email}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
		} else if (!emails.containsKey(email) && StringUtils.isNotBlank(email)) {
			emails.put(email, idNo);
		}
	}

	private void checkRequestSignerEmail(DocumentConfinsRequestBean documentConfinsRequest, AuditContext audit) {		
		for (SignerBean signerBean : documentConfinsRequest.getSigner()) {
			if (StringUtils.isBlank(signerBean.getEmail())) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
					new Object[] {MAP_KEY_EMAIL}, this.retrieveLocaleAudit(audit)), ReasonDocument.EMAIL_NOT_EXISTS);
			}
		}
	}
	
	private void rejectUnregisteredSigner(DocumentConfinsRequestBean[] requests, MsVendor vendor, AuditContext audit) {
		for (DocumentConfinsRequestBean request : requests) {
			for (SignerBean signer : request.getSigner()) {
				MsLov signerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, signer.getSignerType());
				
				if(!signer.getSignerType().contains("MF") && signer.getSignAction().equals("at")) {
					throw new UserException(messageSource.getMessage("businesslogic.user.signernotallowed",
							new String[] {signer.getSignerType(), signer.getUserPhone()}, retrieveLocaleAudit(audit)), ReasonUser.PARAM_INVALID);
				}
				rejectUnregisteredNik(signer.getIdNo(), vendor.getVendorCode(), signerType.getDescription(), audit);
				if(!signer.getSignerType().equals("MF") || !signer.getSignAction().equals("at")) {
					rejectUnregisteredPhone(signer.getUserPhone(), vendor.getVendorCode(), signerType.getDescription(), audit, signer.getIdNo());
				}
				rejectUnregisteredEmail(signer.getEmail(), vendor.getVendorCode(), signerType.getDescription(), audit);
			}
		}
	}
	
	private void rejectUnregisteredPhone(String phone, String vendorCode, String signerType, AuditContext audit, String idNo) {
		if (StringUtils.isBlank(phone)) {
			return;
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		
		AmMsuser userByIdNo = daoFactory.getUserDao().getUserByIdNo(idNo);
		List<MsVendorRegisteredUser> lVruByPhone = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(phone);
		List<String> lVendorCodeByPhoneList = new ArrayList<>();
		for(MsVendorRegisteredUser vru : lVruByPhone) {
			lVendorCodeByPhoneList.add(vru.getMsVendor().getVendorCode());
		}
		
        String phoneNumberForVuserIdNo = null;
        
        if(null != userByIdNo) {
            MsVendorRegisteredUser vuserByIdNo = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(userByIdNo.getIdMsUser(), vendorCode);
            phoneNumberForVuserIdNo = personalDataEncLogic.decryptToString(vuserByIdNo.getPhoneBytea());
        }
        
        if (null == user && null == userByIdNo) {
        	throw new UserException(messageSource.getMessage("businesslogic.user.phonenotregistered",
					new String[] {signerType, phone}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
        }
		
		if (null == user) {
			throw new UserException(messageSource.getMessage("businesslogic.document.invalidnikphone",
					new String[] {phoneNumberForVuserIdNo, phone}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendorCode);
		if (null == vendorUser || !"1".equals(vendorUser.getIsRegistered())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.phonenotregistered",
					new String[] {signerType, phone}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
		}
		
		if (!"1".equals(vendorUser.getIsActive())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.phonenotactivated",
					new String[] {signerType, phone}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
		}
	}
	
	private void rejectUnregisteredEmail(String email, String vendorCode, String signerType, AuditContext audit) {
		if (StringUtils.isBlank(email)) {
			return;
		}
		
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		if (null == user) {
			throw new UserException(messageSource.getMessage("businesslogic.user.emailnotregistered",
					new String[] {signerType, email}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_EMAIL);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendorCode);
		if (null == vendorUser || !"1".equals(vendorUser.getIsRegistered())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.emailnotregistered",
					new String[] {signerType, email}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_EMAIL);
		}
		
		if (!"1".equals(vendorUser.getIsActive())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.emailnotactivated",
					new String[] {signerType, email}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
		}
		
	}
	
	private void rejectUnregisteredNik(String nik, String vendorCode, String signerType, AuditContext audit) {
		if (StringUtils.isBlank(nik)) {
			return;
		}
		
		AmMsuser user = daoFactory.getUserDao().getUserByIdNo(nik);
		if (null == user) {
			throw new UserException(messageSource.getMessage("businesslogic.user.niknotregistered",
					new String[] {signerType, nik}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_NIK);
		}
		
		MsVendorRegisteredUser vendorUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendorCode);
		if (null == vendorUser || !"1".equals(vendorUser.getIsRegistered())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.niknotregistered",
					new String[] {signerType, nik}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_NIK);
		}
		
		if (!"1".equals(vendorUser.getIsActive())) {
			throw new UserException(messageSource.getMessage("businesslogic.user.niknotactivated",
					new String[] {signerType, nik}, retrieveLocaleAudit(audit)), ReasonUser.UNREGISTERED_PHONE);
		}
	}
	
	private MsOffice checkOfficeSendDoc(String officeCode, String officeName, MsTenant tenant, MsRegion region, AuditContext audit) {
		MsOffice officeBean = daoFactory.getOfficeDao().getActiveOfficeByOfficeCodeAndTenantCode(officeCode, tenant.getTenantCode()) ;
		if(null == officeBean) {
			officeBean = new MsOffice();
			officeBean.setOfficeCode(StringUtils.upperCase(officeCode));
			officeBean.setIsActive("1");
			officeBean.setOfficeName(StringUtils.upperCase(officeName));
			officeBean.setMsTenant(tenant);
			officeBean.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			officeBean.setDtmCrt(new Date());
			officeBean.setMsRegion(region);
			daoFactory.getOfficeDao().insertOffice(officeBean);
		} else if (null == officeBean.getMsRegion()) {
			officeBean.setMsRegion(region);
			daoFactory.getOfficeDao().updateOffice(officeBean);
		}
		
		return officeBean;
	}
	
	private MsBusinessLine checkBusinessLineSendDoc(String businessLineCode, String businessLineName, MsTenant tenant, AuditContext audit) {
		MsBusinessLine businessLine = daoFactory.getBusinessLineDao().getBusinessLineByCodeAndTenant(businessLineCode, tenant.getTenantCode());
		if (null == businessLine) {
			businessLine = new MsBusinessLine();
			businessLine.setDtmCrt(new Date());
			businessLine.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			businessLine.setBusinessLineCode(StringUtils.upperCase(businessLineCode));
			businessLine.setBusinessLineName(StringUtils.upperCase(businessLineName));
			businessLine.setMsTenant(tenant);
			daoFactory.getBusinessLineDao().insertBusinessLine(businessLine);
		}
		
		return businessLine;
	}
	
	private MsRegion checkRegionSendDoc(String regionCode, String regionName, MsTenant tenant, AuditContext audit) {
		MsRegion region = daoFactory.getRegionDao().getRegionByCodeAndTenant(regionCode, tenant.getTenantCode());
		if (null == region) {
			region = new MsRegion();
			region.setDtmCrt(new Date());
			region.setUsrCrt(StringUtils.upperCase(audit.getCallerId()));
			region.setMsTenant(tenant);
			region.setRegionCode(StringUtils.upperCase(regionCode));
			region.setRegionName(StringUtils.upperCase(regionName));
			daoFactory.getRegionDao().insertRegion(region);
		}
		
		return region;
	}
	
	private void emailCreatorAndEmailServiceSetting(SignerBean bean, Map<String, Object> checked, MsTenant tenant, MsVendor vendor, List<String> emailListed, Set<Map<String, Object>> userEmailSet, AuditContext audit) throws ParseException, IOException {
		//validasi jika sudah ada user yang memiliki nik yang sama
		AmMsuser userById = daoFactory.getUserDao().getUserByIdNo(bean.getIdNo());
		AmMsuser userByPhone = daoFactory.getUserDao().getActiveUserByPhone(bean.getUserPhone());
		List<String> vendors = daoFactory.getVendorRegisteredUserDao().getUserRegisteredVendorsByNik(bean.getIdNo());
		if (StringUtils.isBlank(bean.getEmail())) {
			if (!checked.containsKey(bean.getIdNo()) && ((null == userById && null == userByPhone) || !vendors.contains(vendor.getVendorCode()))) {
				if (tenant.getEmailService().equals("1") || bean.getSignerType().contains(GlobalVal.CODE_LOV_SIGNER_TYPE_MF)) {
					String email = emailLogic.createEmail(bean, tenant);
					bean.setEmail(email);
					bean.setEmailService("1");
				} else {
					throw new UserException(messageSource.getMessage("businesslogic.document.emptyemail", 
							null, this.retrieveLocaleAudit(audit)), ReasonUser.EMAIL_EMPTY);
				}
				checked.put(bean.getIdNo(), bean);
			} else if (checked.containsKey(bean.getIdNo())) {
				SignerBean checkedBean = (SignerBean) checked.get(bean.getIdNo());
				bean.setEmail(checkedBean.getEmail());
				bean.setEmailService(checkedBean.getEmailService());
			} else if (userById != null) {
				MsVendorRegisteredUser vruById = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(userById.getLoginId(), vendor.getVendorCode());
				bean.setEmail(vruById.getSignerRegisteredEmail());
				bean.setEmailService(vruById.getEmailService());
			} 
		} else {
			bean.setEmailService("0");
		}
		
		MsVendorRegisteredUser vru = null;
		if (null != userById) {
			vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(userById.getIdMsUser(), vendor.getVendorCode());
		}
		
		if (null != vru) {
			bean.setEmail(vru.getSignerRegisteredEmail());
			bean.setEmailService(vru.getEmailService());
		}
		
		bean.setLoginId(bean.getEmail());
		
		TrSigningProcessAuditTrail auditTrail = setAuditTrail(userById, vru, bean, tenant, vendor, audit);
		
		Map<String, Object> email = new HashMap<>();
		email.put(MAP_KEY_EMAIL, bean.getEmail());
		email.put(MAP_KEY_EMAIL_SENT, false);
		email.put(MAP_KEY_PHONE, bean.getUserPhone());
		email.put(MAP_KEY_AUDIT_TRAIL, auditTrail);
		email.put(MAP_KEY_NIK, bean.getIdNo());
		if (!emailListed.contains(bean.getEmail())) { //&& (!bean.getSignAction().equals("at") || StringUtils.isBlank(vru.getVendorUserAutosignKey()))
			userEmailSet.add(email);
			emailListed.add(bean.getEmail());
		}
	}
	
	private TrSigningProcessAuditTrail setAuditTrail(AmMsuser user, MsVendorRegisteredUser vru, SignerBean signer, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.SEND_DOC.getLovCode());
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setResultStatus("1");
		
		if (null != user) {
			auditTrail.setAmMsUser(user);
			if (null != vru) {
				auditTrail.setAmMsUser(user);
				auditTrail.setEmail(vru.getSignerRegisteredEmail());
				auditTrail.setHashedPhoneNo(vru.getHashedSignerRegisteredPhone());
				auditTrail.setPhoneNoBytea(vru.getPhoneBytea());
				NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, vru.getEmailService());
				settingNotifMediaAndGateway(auditTrail, type);
			} else {
				auditTrail.setEmail(signer.getEmail());
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(signer.getUserPhone()));
				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(signer.getUserPhone()));
				NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, signer.getEmailService());
				settingNotifMediaAndGateway(auditTrail, type);
			}
		}
		
		return auditTrail;
	}
	
	private TrSigningProcessAuditTrail setAuditTrailFullApi(SendDocFullApiSignerBean signer, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		MsLov processType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_PROCESS_TYPE_SIGN_REQUEST);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, NotificationSendingPoint.SEND_DOC.getLovCode());
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signer.getEmail(), false, audit);
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(user.getIdMsUser(), vendor.getVendorCode());
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		auditTrail.setUsrCrt(audit.getCallerId());
		auditTrail.setDtmCrt(new Date());
		auditTrail.setMsTenant(tenant);
		auditTrail.setMsVendor(vendor);
		auditTrail.setLovProcessType(processType);
		auditTrail.setLovSendingPoint(sendingPoint);
		auditTrail.setResultStatus("1");
		
		if (null != user) {
			auditTrail.setAmMsUser(user);
			if (null != vru) {
				auditTrail.setAmMsUser(user);
				auditTrail.setEmail(vru.getSignerRegisteredEmail());
				auditTrail.setHashedPhoneNo(vru.getHashedSignerRegisteredPhone());
				auditTrail.setPhoneNoBytea(vru.getPhoneBytea());
				if ("1".equals(tenant.getSignRequestNotification())) {
					NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, vru.getEmailService());
					settingNotifMediaAndGateway(auditTrail, type);
				}
			} else {
				auditTrail.setEmail(signer.getEmail());
				auditTrail.setHashedPhoneNo(MssTool.getHashedString(signer.getTlp()));
				auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(signer.getTlp()));
				if ("1".equals(tenant.getSignRequestNotification())) {
					NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, signer.getEmailService());
					settingNotifMediaAndGateway(auditTrail, type);
				}
			}
		}
		
		return auditTrail;
	}
	
	private void settingNotifMediaAndGateway(TrSigningProcessAuditTrail auditTrail, NotificationType type) {
		if (type.compareTo(NotificationType.EMAIL) == 0) {
			auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_EMAIL);
		} else {
			if (type.compareTo(NotificationType.WHATSAPP) == 0) {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
				auditTrail.setNotificationVendor(gateway.getDescription());
			} else if (type.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_WA);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);
				auditTrail.setNotificationVendor(gateway.getDescription());
			} else {
				auditTrail.setNotificationMedia(GlobalVal.CODE_LOV_MESSAGE_MEDIA_SMS);
				String code = type.compareTo(NotificationType.SMS_JATIS) == 0 ? GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS: GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST;
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, code);
				auditTrail.setNotificationVendor(gateway.getDescription());
			}
		}
	}
	
	private void checkSignerPhoneNumber(Set<Map<String, Object>> userEmailSet, SignerBean[] signer) {
		for(Map<String, Object> map : userEmailSet) {
			for (SignerBean bean : signer) {
				if (map.get(MAP_KEY_EMAIL).equals(bean.getEmail())) {
					map.replace(MAP_KEY_PHONE, bean.getUserPhone());
				}
			}
		}
	}
	
	private void checkDuplicateRefNumber(String refNumber, MsTenant tenant, AuditContext audit) {
		TrDocumentH docH = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(refNumber, tenant.getTenantCode());
		String newRefNumber = StringUtils.EMPTY;
		if (null != docH) {
			int x = 1;
			while (true) {
				newRefNumber = refNumber + "_" + x;
				TrDocumentH docHWithNewRefNumber = daoFactory.getDocumentDao().getDocumentHeaderByRefNoAndTenantCode(newRefNumber, tenant.getTenantCode());		
				if (null == docHWithNewRefNumber) {
					docH.setDtmUpd(new Date());
					docH.setUsrUpd(audit.getCallerId());
					docH.setRefNumber(newRefNumber);
					docH.setResultUrl(null);
					docH.setUrlUpload(null);
					docH.setIsActive("0");
					daoFactory.getDocumentDao().updateDocumentH(docH);
					break;
				}
				x++;
			}
		}
		
		List<TrBalanceMutation> listBm = daoFactory.getBalanceMutationDao().getListBalanceMutationByRefNoAndTenantCode(refNumber, 
				tenant.getTenantCode());
		if (!listBm.isEmpty() && StringUtils.isNotBlank(newRefNumber)) {
			for (TrBalanceMutation bm : listBm) {
				bm.setDtmUpd(new Date());
				bm.setUsrUpd(audit.getCallerId());
				bm.setRefNo(newRefNumber);
			}
			daoFactory.getBalanceMutationDao().updateListBalanceMutation(listBm);
		}
		
	}
	
	private void insertErrorHistorySendDoc(DocumentConfinsRequestBean[] documentConfinsRequests, String msg, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		List<SignerBean> signers = new ArrayList<>();
		List<String> signerTypes = new ArrayList<>();
		String custName = "";
		for (DocumentConfinsRequestBean documentConfinsRequest : documentConfinsRequests) {
			for(SignerBean bean : documentConfinsRequest.getSigner()) {
				if (!signerTypes.contains(bean.getSignerType())) {
					signerTypes.add(bean.getSignerType());
					signers.add(bean);
				}
				
				if (StringUtils.isBlank(custName)) {
					custName = bean.getUserName();
				}
			}
		}
		
		this.inserErrorHistory(documentConfinsRequests[0].getBusinessLineName(), documentConfinsRequests[0].getRegionName(), documentConfinsRequests[0].getOfficeName(),
				documentConfinsRequests[0].getReferenceNo(), signers, custName, msg, GlobalVal.ERROR_TYPE_REJECT, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);
		
	}
	
	private void insertErrorHistorySendDocFullApi(SendDocFullApiRequestBean request, String msg, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		List<SignerBean> signerBeans = new ArrayList<>();
		List<String> signerTypes = new ArrayList<>();
		String custName = "";
		for(SendDocFullApiSignerBean bean : request.getSigners()) {
			if (!signerTypes.contains(bean.getSignerType())) {
				signerTypes.add(bean.getSignerType());
				SignerBean toAdd = new SignerBean();
				String name = StringUtils.isBlank(bean.getEmail()) ? bean.getTlp() : bean.getEmail();
				toAdd.setUserName(name);
				toAdd.setSignerType(bean.getSignerType());
				toAdd.setIdNo(bean.getIdKtp());
				
				signerBeans.add(toAdd);
				
				if (StringUtils.isBlank(custName)) {
					custName = name;
				}
			}
		}
		
		this.inserErrorHistory(request.getBusinessLineName(), request.getRegionName(), request.getOfficeName(),
				request.getReferenceNo(), signerBeans, custName, msg, GlobalVal.ERROR_TYPE_REJECT, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);
		
	}
	
	private void inserErrorHistory(String bizLine, String region, String office, String refNo, List<SignerBean> signers, String custName, String msg, String errType, MsTenant tenant, MsVendor vendor, String module, AuditContext audit) {
		MsLov lovModul = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_ERR_HIST_MODULE, module);
				
		TrErrorHistory errorHistory = new TrErrorHistory();
		errorHistory.setMsLov(lovModul);
		
		errorHistory.setCustName(custName);
		errorHistory.setBusinessLine(StringUtils.upperCase(bizLine));
		errorHistory.setRefNumber(StringUtils.upperCase(refNo));
		errorHistory.setRegion(StringUtils.upperCase(region));
		errorHistory.setOffice(StringUtils.upperCase(office));
		errorHistory.setMsTenant(tenant);
		errorHistory.setMsVendor(vendor);
		errorHistory.setErrorType(errType);
		errorHistory.setErrorDate(new Date());
		errorHistory.setErrorMessage(StringUtils.left(msg, 300));
		errorHistory.setUsrCrt(audit.getCallerId());
		errorHistory.setDtmCrt(new Date());
		errorHistory.setRerunProcess("0");
		daoFactory.getErrorHistoryDao().insertErrorHistory(errorHistory);
				
		for (SignerBean bean : signers) {
			insertErrorHistoryUserDetail(bean, errorHistory, audit);
		}
	}
	
	private void insertErrorHistoryUserDetail(SignerBean signer, TrErrorHistory errHist, AuditContext audit) {
		TrErrorHistoryUserDetail userDetail = new TrErrorHistoryUserDetail();
		userDetail.setDtmCrt(new Date());
		userDetail.setUsrCrt(audit.getCallerId());
		MsLov msLov = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_SIGNER_TYPE, signer.getSignerType());
		userDetail.setMsLov(msLov);
		userDetail.setTrErrorHistory(errHist);
		userDetail.setUserIdno(signer.getIdNo());
		userDetail.setUserName(signer.getUserName());
		
		daoFactory.getErrorHistoryDao().insertErrorHistoryUserDetail(userDetail);
	}
	
	private void deactivateInvLink(String email, String phone, String idNo, String vendorCode, AuditContext audit) {
		TrInvitationLink invLink = null;
		MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(vendorCode);
		if (StringUtils.isNotBlank(email)) {
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailAndIdMsVendor(email, vendor.getIdMsVendor());
		} 
		
		if (null == invLink && StringUtils.isNotBlank(phone)){
			invLink = daoFactory.getInvitationLinkDao().getInvitationLinkByRecieverDetailAndIdMsVendor(phone, vendor.getIdMsVendor());
			if (null == invLink) {
				invLink = daoFactory.getInvitationLinkDao().getInvitationByPhoneAndVendorCode(phone, vendorCode);
			}
		}
		
		if (null == invLink) {
			invLink = daoFactory.getInvitationLinkDao().getInvitationByIdNoAndVendorCode(idNo, vendorCode);
		}
		
		if (null != invLink) {
			invLink.setIsActive("0");
			invLink.setDtmUpd(new Date());
			invLink.setUsrUpd("Send Doc by " + audit.getCallerId());
			daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
		}
	}
	
	public String[] checkIfUserIsRegistered(SignerBean[] signer, MsTenant tenant, MsVendor vendor, MsOffice office,
			String documentTemplate, AuditContext audit) throws NoSuchAlgorithmException {
		String[] passwords = new String[signer.length];
		int i = 0;
		
		List<MsDocTemplateSignLoc> listSL = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeAndIdTenant(documentTemplate, tenant.getIdMsTenant());
		List<String> signerType = new ArrayList<>();
		for (MsDocTemplateSignLoc sl : listSL) {
			if (sl.getMsLovByLovSignerType() != null && !signerType.contains(sl.getMsLovByLovSignerType().getCode())) {
				signerType.add(sl.getMsLovByLovSignerType().getCode());
			}
		}
		
		for (SignerBean signerBean : signer) {	
			if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
				this.checkRegisteredTekenAja(signerBean, vendor, tenant, audit);
			}
			
			AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signerBean.getIdNo());
			String randomPassword = userLogic.generateRandomPassword();
			if (signerType.contains(signerBean.getSignerType())) {
				if(null != user) {
					TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
					//Penjaagaan jika user belum ganti password dan belum ada permintaan tanda tangan
					// dan belum di-overwrite passwordnya di dokumen sebelumnya dan usernya dibuat melalui inv link
					if ("1".equals(user.getChangePwdLogin()) 
							&& null == docH
							&& user.getPassword().equals("newInv")) {
						passwords[i] = randomPassword;
						user.setPassword(PasswordHash.createHash(randomPassword));
						user.setDtmUpd(new Date());
						user.setUsrUpd(audit.getCallerId());
						daoFactory.getUserDao().updateUser(user);
						LOG.info("Send Document: Password update for user with login ID: {} and password: {}", user.getLoginId(), randomPassword);
					} else {
						passwords[i] = null;
					}
					this.checkDataForExistingUser(signerBean, user, tenant, vendor, audit);
					this.checkIfUserHaveExistingDoc(user, office);
				} else {
					passwords[i] = randomPassword;
					user = new AmMsuser();	
					this.insertNewUser(user, signerBean,randomPassword, office, audit);
					LOG.info("Send Document: User created with login ID: {} and password: {}", user.getLoginId(), randomPassword);
					
					this.insertNewUserPersonalData(user, signerBean, audit );
					this.insertPasswordHist(user, audit);
					this.insertRoleNewUser(user, signerBean.getSignerType(), tenant, audit);
					this.insertUseroftenant(user, tenant, audit);
					this.insertVendorRegisteredUser(user, vendor, signerBean, audit);
				}
			} else {
				passwords[i] = null;
			}
			
			i+=1;
		}
		return passwords;
	}
	
	private void checkRegisteredTekenAja(SignerBean signerBean, MsVendor vendor, MsTenant tenant, AuditContext audit) {
		TknajRegisterCekResponse regcek = tekenajaLogic.registerCek(signerBean.getIdNo(), TekenAjaConstant.REGCEK_ACTION_CHECK_NIK,  vendor, tenant, audit);
		if (GlobalVal.TEKEN_REGCEK_USER_EXIST_VERIFIED.equals(regcek.getCode())) {
			signerBean.setIsRegistered("1");
			signerBean.setIsActive("1");
		} else if (GlobalVal.TEKEN_REGCEK_USER_EXISTS_UNVERIFIED.equals(regcek.getCode())) {
			signerBean.setIsRegistered("1");
			signerBean.setIsActive("0");
		} else {
			signerBean.setIsRegistered("0");
			signerBean.setIsActive("0");
		}
		
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(signerBean.getLoginId(), GlobalVal.VENDOR_CODE_TEKENAJA);
		if (null != vru) {
			vru.setIsActive(signerBean.getIsActive());
			vru.setIsRegistered(signerBean.getIsRegistered());
			daoFactory.getVendorRegisteredUserDao().updateVendorRegisteredUser(vru);
		}
	}
	
	private void checkIfUserHaveExistingDoc(AmMsuser user, MsOffice office) {
		TrDocumentH docH = daoFactory.getDocumentDao().getEarliestAgreement(user);
		if (docH == null) {
			user.setMsOffice(office);
			daoFactory.getUserDao().updateUser(user);
		}
	}
	
	private void checkIdNoPhoneAndEmail(String idNo, String phone, String email, String vendorCode, AuditContext audit) {
		if(StringUtils.isNotBlank(email)) {
			AmGeneralsetting gsEmail = commonLogic.getGeneralSettingByCode(AmGlobalKey.GENERALSETTING_REGEX_EMAIL_FORMAT, audit);
			String emailRegex = gsEmail != null && StringUtils.isNotBlank(gsEmail.getGsValue()) ? gsEmail.getGsValue() : regexEmail;
			
			if (!StringUtils.upperCase(email).matches(emailRegex)) {
				throw new UserException(messageSource.getMessage("businesslogic.user.invalidemail", 
						new Object[] {email}, this.retrieveLocaleAudit(audit)), ReasonUser.PARAM_INVALID);
			}
		}
		
		AmGeneralsetting gsPhone = commonLogic.getGeneralSettingByCode(AmGlobalKey.GENERALSETTING_REGEX_PHONE_FORMAT, audit);
		String phoneNoRegex = gsPhone != null && StringUtils.isNotBlank(gsPhone.getGsValue()) ? gsPhone.getGsValue() : regexPhone;
		
		if (!phone.matches(phoneNoRegex)) {
			throw new UserException(messageSource.getMessage("businesslogic.user.invalidphone", 
					new Object[] {phone}, this.retrieveDefaultLocale()), ReasonUser.PARAM_INVALID);
		}
		
		AmMsuser userByIdNo = daoFactory.getUserDao().getUserByIdNo(idNo);
		AmMsuser userByPhone = userValidatorLogic.validateGetUserByPhone(phone, false, audit);
		List<MsVendorRegisteredUser> lVruByPhone = daoFactory.getVendorRegisteredUserDao().getListVendorRegisteredUserByPhone(phone);
		List<String> lVendorCodeByPhoneList = new ArrayList<>();
		for(MsVendorRegisteredUser vru : lVruByPhone) {
			lVendorCodeByPhoneList.add(vru.getMsVendor().getVendorCode());
		}
		
		String phoneNumberForVuserIdNo = null;
		String idNoForVuserPhone = null;
		if(null != userByIdNo) {
			MsVendorRegisteredUser vuserByIdNo = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(userByIdNo.getIdMsUser(), vendorCode);
			if (vuserByIdNo != null) {
				phoneNumberForVuserIdNo = personalDataEncLogic.decryptToString(vuserByIdNo.getPhoneBytea());
			} else {
				PersonalDataBean pdBean = daoFactory.getUserDao().getUserDataByIdMsUser(userByIdNo.getIdMsUser(), false);
				phoneNumberForVuserIdNo = pdBean.getPhoneRaw();
			}
		}
		if(null != userByPhone) {
			PersonalDataBean personalDataBeanByPhone = daoFactory.getUserDao().getUserDataByIdMsUser(userByPhone.getIdMsUser(), false);
			idNoForVuserPhone = personalDataBeanByPhone.getIdNoRaw();
        }
		
		if(StringUtils.isBlank(email)) {
			if (null != userByIdNo && null == userByPhone && lVendorCodeByPhoneList.contains(vendorCode)) {
				throw new UserException(messageSource.getMessage("businesslogic.document.signernikalreadyexist", 
						new Object[] {idNo}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null == userByIdNo && null != userByPhone && lVendorCodeByPhoneList.contains(vendorCode)) {
				throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_PHONENIK, 
						new Object[] {idNoForVuserPhone, idNo}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null != userByIdNo && null != userByPhone && !userByIdNo.getHashedIdNo().equals(userByPhone.getHashedIdNo())) {
				throw new UserException(messageSource.getMessage("businesslogic.document.invalidnikphone", 
						new Object[] {phoneNumberForVuserIdNo, phone}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null != userByIdNo && null != userByPhone) {
				//existing user. email created by esign
			}
			
			if (null == userByIdNo && null == userByPhone) {
				//new user, create email
			}
			return;
		}
		
		AmMsuser userByEmail = userValidatorLogic.validateGetUserByEmailv2(email, false, audit);
		if (null == userByEmail) {
			if (null != userByIdNo && null == userByPhone && lVendorCodeByPhoneList.contains(vendorCode)) {
				throw new UserException(messageSource.getMessage("businesslogic.document.signernikalreadyexist", 
						new Object[] {idNo}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			if (null == userByIdNo && null != userByPhone && lVendorCodeByPhoneList.contains(vendorCode)) {
				throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_PHONENIK, 
						new Object[] {idNoForVuserPhone, idNo}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			if (null != userByIdNo && null != userByPhone && lVendorCodeByPhoneList.contains(vendorCode)) {
				throw new UserException(messageSource.getMessage("businesslogic.document.invalidnikemail", 
						new Object[] {userByIdNo.getLoginId(), email}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
				
			if (null == userByIdNo && null == userByPhone) {
				//new user, create email
			}
		} else {
			MsVendorRegisteredUser vuserByEmail = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdMsUserAndVendorCode(userByEmail.getIdMsUser(), vendorCode);
			String phoneNumberForVuserEmail;
			if (null != vuserByEmail) {
				phoneNumberForVuserEmail = personalDataEncLogic.decryptToString(vuserByEmail.getPhoneBytea());
			} else {
				PersonalDataBean pdBean = daoFactory.getUserDao().getUserDataByIdMsUser(userByEmail.getIdMsUser(), false);
				phoneNumberForVuserEmail = pdBean.getPhoneRaw();
			}
			
			if (null != userByIdNo && !userByEmail.getHashedIdNo().equals(userByIdNo.getHashedIdNo())) {
				throw new UserException(messageSource.getMessage("businesslogic.document.signernikemailalreadyexist", 
						new Object[] {idNo, email}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null == userByIdNo) {
				throw new UserException(messageSource.getMessage("businesslogic.document.mismatchemailandid",
						new Object[] {idNo,email}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null != userByPhone && !userByEmail.getHashedIdNo().equals(userByPhone.getHashedIdNo())) {
				throw new UserException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_PHONENIK, 
						new Object[] {idNoForVuserPhone, idNo}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
			
			if (null == userByPhone) {
				throw new UserException(messageSource.getMessage("businesslogic.document.invalidemailphone",
						new Object[] {phoneNumberForVuserEmail, phone}, this.retrieveLocaleAudit(audit)), ReasonUser.ERROR_EXIST);
			}
		}
		
	}
	
	private void checkDataForExistingUser(SignerBean bean, AmMsuser user, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		AmMemberofrole mor = daoFactory.getRoleDao().getMemberofroleByLoginIdRoleTenantCode(user.getLoginId(), tenant.getTenantCode());
		if (null == mor) {
			this.insertRoleNewUser(user, bean.getSignerType(), tenant, audit);
		}
		
		MsUseroftenant uot = daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(user.getLoginId(), tenant.getTenantCode());
		if (null == uot ) {
			this.insertUseroftenant(user, tenant, audit);
		}
		
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(user.getLoginId(), vendor.getVendorCode());
		if (null == vru) {
			vru = this.insertVendorRegisteredUser(user, vendor, bean, audit);
		}
		
		if (!GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			bean.setIsActive(vru.getIsActive());
			bean.setIsRegistered(vru.getIsRegistered());
		}
		
		String rawPhone = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		if (!rawPhone.equals(bean.getUserPhone())) {
			bean.setUserPhone(rawPhone);
		}
		
		if ("1".equals(user.getIsDormant())) {
			user.setIsDormant("0");
			user.setUsrUpd(audit.getCallerId());
			user.setDtmUpd(new Date());
			daoFactory.getUserDao().updateUser(user);
		}
	}
	
	private MsVendorRegisteredUser insertVendorRegisteredUser(AmMsuser user, MsVendor vendor, SignerBean bean, AuditContext audit) {
		MsVendorRegisteredUser registeredUser = new MsVendorRegisteredUser();
		registeredUser.setSignerRegisteredEmail(StringUtils.upperCase(bean.getEmail()));
		registeredUser.setIsActive(StringUtils.isNotBlank(bean.getIsActive()) ? bean.getIsActive() : "0");
		registeredUser.setIsRegistered(StringUtils.isNotBlank(bean.getIsRegistered()) ? bean.getIsRegistered() : "0");
		registeredUser.setUsrCrt(audit.getCallerId());
		registeredUser.setDtmCrt(new Date());
		registeredUser.setAmMsuser(user);
		registeredUser.setMsVendor(vendor);
		registeredUser.setEmailService(bean.getEmailService());
		registeredUser.setPhoneBytea(personalDataEncLogic.encryptFromString(bean.getUserPhone()));
		registeredUser.setHashedSignerRegisteredPhone(MssTool.getHashedString(bean.getUserPhone()));
		vendorLogic.insertVendorRegisteredUser(registeredUser);
		
		return registeredUser;
	}
	
	private void insertUseroftenant(AmMsuser user, MsTenant tenant, AuditContext audit) {
		userLogic.insertUserofTenant(user, tenant, audit);
	}

	private void insertPasswordHist(AmMsuser newUser, AuditContext audit) {
		userLogic.insertPasswordHist(newUser, audit);	
	}

	private void insertRoleNewUser(AmMsuser newUser, String signerType, MsTenant tenant, AuditContext audit) {
		String roleCode;
		if(signerType.equals(GlobalVal.CODE_LOV_SIGNER_TYPE_CUST) || 
				signerType.equals(GlobalVal.CODE_LOV_SIGNER_TYPE_MF)) {
			roleCode = signerType;
		} else {
			roleCode = GlobalVal.ROLE_CUSTOMER;
		}
		AmMsrole role = daoFactory.getRoleDao().getRoleByCodeAndTenantCode(roleCode, tenant.getTenantCode());
		userLogic.insertRoleNewUser(newUser, role, audit);	
	}

	private void insertNewUserPersonalData(AmMsuser newUser, SignerBean signerBean, AuditContext audit) {
		userLogic.insertNewUserPersonalData(newUser, signerBean, audit);
	}

	private void insertNewUser(AmMsuser newUser, SignerBean signerBean, String randomPassword, MsOffice office, AuditContext audit) throws NoSuchAlgorithmException {
		userLogic.insertNewUser(newUser, signerBean, randomPassword, office, audit);
	}
	
	private TrDocumentD insertDocumentConfins(DocumentConfinsRequestBean documentConfinsRequest, MsTenant tenant,
			MsVendor vendor, TrDocumentH docH, SignerBean[] docSigner, String[] signerPassword, String[] mapNewUserPassword, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, IOException, InterruptedException {	
		TrDocumentD newDoc = new TrDocumentD();
		newDoc.setMsLovByLovSignStatus(daoFactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN));
		newDoc.setUsrCrt(audit.getCallerId());
		newDoc.setDtmCrt(new Date());
		newDoc.setMsTenant(tenant);
		newDoc.setMsVendor(vendor); 
		newDoc.setTrDocumentH(docH);
		newDoc.setRequestDate(new Date());
		newDoc.setTotalSigned((short) 0);
		newDoc.setTotalStamping((short) 0);
		newDoc.setSendStatus((short) 0);
		newDoc.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		newDoc.setMsLovByLovSignStatus(
				daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN));
		
		MsDocTemplate docTemp = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(documentConfinsRequest.getDocumentTemplateCode(), tenant.getTenantCode());
		newDoc.setMsDocTemplate(docTemp);
		String isSeq = StringUtils.isBlank(documentConfinsRequest.getIsSequence()) ? docTemp.getIsSequence() : documentConfinsRequest.getIsSequence();
		if ("1".equals(isSeq) && "0".equals(docTemp.getIsSequence())) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.cannotbesequential", new Object[] {docTemp.getDocTemplateCode()}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.CANT_SEQ_SIGN);
		}
		newDoc.setIsSequence(isSeq);
		newDoc.setUseSignQr(StringUtils.isBlank(docTemp.getUseSignQr()) ? "0" : docTemp.getUseSignQr());
		newDoc.setPrioritySequence(null == docTemp.getPrioritySequence() ? 0 : docTemp.getPrioritySequence());
		
		MsLov vendorSignPaymentType = daoFactory.getLovDao().getMsLovByIdLov(vendor.getMsLovVendorSignPaymentType().getIdLov());
		if (GlobalVal.CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_MIX.equals(vendorSignPaymentType.getCode())) {
			newDoc.setMsLovByLovPaymentSignType(docTemp.getMsLovPaymentSignType());
		} else if (GlobalVal.CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_SIGN_ONLY.equals(vendorSignPaymentType.getCode())) {
			MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE, GlobalVal.PAY_SIGN_TYPE_SIGN);
			newDoc.setMsLovByLovPaymentSignType(signType);
		} else {
			MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE, GlobalVal.PAY_SIGN_TYPE_DOC);
			newDoc.setMsLovByLovPaymentSignType(signType);
		}

		daoFactory.getDocumentDao().insertDocumentDetail(newDoc);
		
		short totalMeterai = 0;
		short totalSign = 0;
		short signerRequestCounter = 0;
		List<SignerBean> listDocSigner = new ArrayList<>();
		List<String> listSignerPassword = new ArrayList<>();
		List<MsDocTemplateSignLoc> signLocs = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeAndIdTenant(
				documentConfinsRequest.getDocumentTemplateCode(), tenant.getIdMsTenant());
		
		for(SignerBean signer : documentConfinsRequest.getSigner()) {
			int signerTotalSign = 0;
			for(MsDocTemplateSignLoc sl : signLocs) {
				if(!sl.getMsLovByLovSignType().getCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT) && 
						sl.getMsLovByLovSignerType().getCode().equalsIgnoreCase(signer.getSignerType())) {
					TrDocumentDSign docDSign = new TrDocumentDSign();
					docDSign.setUsrCrt(audit.getCallerId());
					docDSign.setDtmCrt(new Date());
					docDSign.setAmMsuser(daoFactory.getUserDao().getUserByIdNo(signer.getIdNo()));
					docDSign.setTrDocumentD(newDoc);
					docDSign.setSentDate(new Date());
					
					String signActionCode = "at".equals(signer.getSignAction()) ? GlobalVal.CODE_LOV_AUTOSIGN : GlobalVal.CODE_LOV_MANUALSIGN;
					docDSign.setMsLovByLovAutosign(daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN, signActionCode));
					docDSign.setSignLocation(GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode()) ? sl.getSignLocation() : sl.getTekenAjaSignLocation());
					SignatureDetailBean vsl = gson.fromJson(sl.getVidaSignLocation(), SignatureDetailBean.class);
					vsl.setX(Math.round(vsl.getX()));
					vsl.setY(Math.round(vsl.getY()));
					vsl.setW(Math.round(vsl.getW()));
					vsl.setH(Math.round(vsl.getH()));
					docDSign.setVidaSignLocation(gson.toJson(vsl).replace(".0", ""));
					
					SignatureDetailBean psl = gson.fromJson(sl.getPrivySignLocation(), SignatureDetailBean.class);
					psl.setX(Math.round(psl.getX()));
					psl.setY(Math.round(psl.getY()));
					psl.setW(Math.round(psl.getW()));
					psl.setH(Math.round(psl.getH()));
					docDSign.setPrivySignLocation(gson.toJson(psl).replace(".0", ""));
					docDSign.setSignPage(sl.getSignPage());
					docDSign.setSeqNo(sl.getSeqNo());
					docDSign.setMsLovByLovSignType(sl.getMsLovByLovSignType());
					docDSign.setMsLovByLovSignerType(sl.getMsLovByLovSignerType());
					if (signer.getSignAction().equalsIgnoreCase(GlobalVal.CODE_DIGISIGN_AUTOSIGN)) {
						
						if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
							this.autosign(signer, docH, newDoc, docDSign, docH.getMsOffice(), docH.getMsBusinessLine(), audit);
						} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
							String ipAddress = MssTool.getApplicationIpAddress();
							boolean qrEnable = "1".equals(newDoc.getUseSignQr());

							PoaRequest poaRequest = vidaLogic.buildPoaRequest(signer.getEmail(), signer.getIdNo(), documentConfinsRequest.getDocumentFile(), sl.getVidaSignLocation(), sl.getSignPage(), qrEnable, ipAddress, docDSign, tenant, audit);
							
							if (!(poaRequest.getRequestInfo() == null && poaRequest.getSigner() == null && poaRequest.getSigningInfo() == null)) {
								PoaResponse response = vidaLogic.doPoa(poaRequest, tenant, newDoc, docH, docDSign, audit);
								if (response.getErrors() != null || response.getCode() != 2) {
									throw new DocumentException(CONST_FAIL_POA_VIDA + response.getErrors()[0].getTitle(), 
										ReasonDocument.AUTOSIGN_FAILED);
								} else {
									documentConfinsRequest.setDocumentFile(response.getSignedDocument());
									docDSign.setPoaId(poaRequest.getSigner().getKeyId());
								}
							}
						} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
//							tidak update data
						}
					}
					daoFactory.getDocumentDao().insertDocumentDetailSign(docDSign);
					if (!listDocSigner.contains(signer)) {
						listDocSigner.add(signer);
						listSignerPassword.add(mapNewUserPassword[signerRequestCounter]);
					}
					
					totalSign++;
					signerTotalSign++;
				}
			}
			signerRequestCounter++;
			
			if (signerTotalSign > 1 && GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
				throw new DocumentException(messageSource.getMessage(MSG_MAXSIGNEXCEEDED, 
						new Object[] {vendor.getVendorName()}, this.retrieveLocaleAudit(audit)), ReasonDocument.MAX_SIGN_EXCEEDED);
			}
		}
		
		totalMeterai = this.insertTrDocumentDStampduty(signLocs, newDoc, audit);
		
		listDocSigner.toArray(docSigner);
		listSignerPassword.toArray(signerPassword);
		
		newDoc.setTotalMaterai(totalMeterai);
		newDoc.setTotalSign(totalSign);
		if (newDoc.getTotalSign().equals(newDoc.getTotalSigned())) {
			docH.setTotalSigned((short) (docH.getTotalSigned() + (short) 1));
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(new Date());
			if (docH.getTotalDocument().equals(docH.getTotalSigned())) {
				docH.setCallbackProcess((short) 1);
			}
			daoFactory.getDocumentDao().updateDocumentH(docH);
			
			MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
			newDoc.setMsLovByLovSignStatus(signStatus);
			newDoc.setCompletedDate(new Date());

			byte[] signedDocument = Base64.getDecoder().decode(documentConfinsRequest.getDocumentFile());
			cloudStorageLogic.storeSignedDocument(newDoc, signedDocument);
		}
		
		daoFactory.getDocumentDao().updateDocumentDetail(newDoc);
		
		return newDoc;
		
	}
	
	private TrDocumentD insertDocumentDetailFullApi(SendDocFullApiRequestBean request, MsTenant tenant,
			MsVendor vendor, TrDocumentH docH, SignerBean[] docSigner, SendDocFullApiResponseBean response, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, IOException, InterruptedException {	
		List<String> trxNos = new ArrayList<>();
		TrDocumentD newDoc = new TrDocumentD();
		newDoc.setMsLovByLovSignStatus(daoFactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN));
		newDoc.setUsrCrt(audit.getCallerId());
		newDoc.setDtmCrt(new Date());
		newDoc.setMsTenant(tenant);
		newDoc.setMsVendor(vendor); 
		newDoc.setTrDocumentH(docH);
		newDoc.setRequestDate(new Date());
		newDoc.setTotalSigned((short) 0);
		newDoc.setTotalStamping((short) 0);
		newDoc.setSendStatus((short) 0);
		newDoc.setSdtProcess(GlobalVal.STEP_ATTACH_METERAI_NOT_STR);
		newDoc.setDocumentName(request.getDocumentName());
		newDoc.setMsLovByLovSignStatus(
				daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS,GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN));
		
		MsDocTemplate docTemp = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
		newDoc.setMsDocTemplate(docTemp);
		
		String isSeq = null != docTemp && StringUtils.isBlank(request.getIsSequence()) ? docTemp.getIsSequence() : request.getIsSequence();
		isSeq = StringUtils.isBlank(isSeq) ? "0" : isSeq;
		if (null != docTemp && "1".equals(isSeq) && "0".equals(docTemp.getIsSequence())) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.cannotbesequential", new Object[] {docTemp.getDocTemplateCode()}, this.retrieveLocaleAudit(audit)),
					ReasonDocument.CANT_SEQ_SIGN);
		}
		newDoc.setIsSequence(isSeq);
		
		MsLov vendorSignPaymentType = daoFactory.getLovDao().getMsLovByIdLov(vendor.getMsLovVendorSignPaymentType().getIdLov());
		if (GlobalVal.CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_MIX.equals(vendorSignPaymentType.getCode())) {
			newDoc.setMsLovByLovPaymentSignType(docTemp.getMsLovPaymentSignType());
		} else if (GlobalVal.CODE_LOV_VENDOR_SIGN_PAYMENT_TYPE_SIGN_ONLY.equals(vendorSignPaymentType.getCode())) {
			MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE, GlobalVal.PAY_SIGN_TYPE_SIGN);
			newDoc.setMsLovByLovPaymentSignType(signType);
		} else {
			MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_PAY_SIGN_TYPE, GlobalVal.PAY_SIGN_TYPE_DOC);
			newDoc.setMsLovByLovPaymentSignType(signType);
		}

		daoFactory.getDocumentDao().insertDocumentDetail(newDoc);
		response.setDocumentId(newDoc.getDocumentId());
		
		short totalMeterai = 0;
		short totalSign = 0;
		short signerRequestCounter = 0;
		List<SendDocFullApiSignerBean> listDocSigner = new ArrayList<>();
		List<MsDocTemplateSignLoc> signLocs = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeAndIdTenant(
				request.getDocumentTemplateCode(), tenant.getIdMsTenant());
		
		Map<String, Short> seqNos = new HashMap<>();
		
		for(SendDocFullApiSignerBean signer : request.getSigners()) {
			AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signer.getEmail(), false, audit);
			PersonalDataBean pdBean = daoFactory.getUserDao().getUserDataByIdMsUser(user.getIdMsUser(), false);
			
			SignerBean sb = new SignerBean();
			sb.setEmail(signer.getEmail());
			sb.setUserPhone(signer.getTlp());
			sb.setIdNo(signer.getIdKtp());
			sb.setSignerType(signer.getSignerType());
			sb.setSignAction(signer.getSignAction());
			sb.setLoginId(signer.getEmail());
			sb.setUserName(user.getFullName());
			sb.setUserAddress(pdBean.getAddressRaw());
			if (null != pdBean.getUserPersonalData().getZipcodeBean()) {
				sb.setKecamatan(pdBean.getUserPersonalData().getZipcodeBean().getKecamatan());
				sb.setKelurahan(pdBean.getUserPersonalData().getZipcodeBean().getKelurahan());
				sb.setProvinsi(pdBean.getUserPersonalData().getZipcodeBean().getProvinsi());
				sb.setKota(pdBean.getUserPersonalData().getZipcodeBean().getKota());
			}
			sb.setUserPob(pdBean.getUserPersonalData().getPlaceOfBirth());
			docSigner[signerRequestCounter] = sb;
			if (StringUtils.isNotBlank(request.getDocumentTemplateCode())) {
				newDoc.setUseSignQr(StringUtils.isBlank(docTemp.getUseSignQr()) ? "0" : docTemp.getUseSignQr());
				totalSign += this.insertDocumentDetailWithTemplate(signLocs, newDoc, signer, trxNos, docH, request, listDocSigner, vendor, tenant, audit);
			} else {
				
				if ("1".equals(request.getIsSequence()) && null == signer.getSeqNo()) {
					throw new DocumentException(messageSource.getMessage("businesslogic.document.seqmustbefilled", null, this.retrieveLocaleAudit(audit)),
							ReasonDocument.SEQ_NO_EMPTY);
				}
				
				if (!seqNos.containsKey(signer.getSignerType()) && !seqNos.containsValue(signer.getSeqNo())) {
					seqNos.put(signer.getSignerType(), signer.getSeqNo());
				} else if ("1".equals(request.getIsSequence()) && seqNos.containsValue(signer.getSeqNo()) && !seqNos.containsKey(signer.getSignerType())) {
					throw new DocumentException(messageSource.getMessage("businesslogic.document.seqnomustbeunique", null, this.retrieveLocaleAudit(audit))
							, ReasonDocument.SEQ_SIGN_NOT_UNIQUE);
				}
				
				newDoc.setUseSignQr(StringUtils.isBlank(request.getUseSignQR()) ? "0" : request.getUseSignQR());
				totalSign += this.insertDocumentDetailWithoutTemplate(newDoc, signer, trxNos, docH, request, listDocSigner, vendor, tenant, audit);
			}
			signerRequestCounter++;
		}
			
		
		response.setTrxNos(trxNos);
		if (!signLocs.isEmpty()) {
			totalMeterai = this.insertTrDocumentDStampduty(signLocs, newDoc, audit);
		} else if (null != request.getStampLocations()) {
			totalMeterai = this.insertTrDocumentDStampdutyFullApi(request.getStampLocations(), newDoc, audit);
		}
		
		newDoc.setTotalMaterai(totalMeterai);
		newDoc.setTotalSign(totalSign);
		if (newDoc.getTotalSign().equals(newDoc.getTotalSigned())) {
			docH.setTotalSigned((short) (docH.getTotalSigned() + (short) 1));
			docH.setUsrUpd(audit.getCallerId());
			docH.setDtmUpd(new Date());
			if (docH.getTotalDocument().equals(docH.getTotalSigned())) {
				docH.setCallbackProcess((short) 1);
			}
			daoFactory.getDocumentDao().updateDocumentH(docH);
			
			MsLov signStatus = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, GlobalVal.CODE_LOV_SIGN_STATUS_COMPLETED);
			newDoc.setMsLovByLovSignStatus(signStatus);
			newDoc.setCompletedDate(new Date());

			byte[] signedDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeSignedDocument(newDoc, signedDocument);
		}
		
		daoFactory.getDocumentDao().updateDocumentDetail(newDoc);
		
		return newDoc;
	}
	
	private int insertDocumentDetailWithTemplate(List<MsDocTemplateSignLoc> signLocs, TrDocumentD newDoc, SendDocFullApiSignerBean signer, 
			List<String> trxNos, TrDocumentH docH, SendDocFullApiRequestBean request, List<SendDocFullApiSignerBean> listDocSigner,
			MsVendor vendor, MsTenant tenant, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, IOException, InterruptedException {
		int totalSign = 0;
		for(MsDocTemplateSignLoc sl : signLocs) {
			if(!sl.getMsLovByLovSignType().getCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT) && 
					sl.getMsLovByLovSignerType().getCode().equalsIgnoreCase(signer.getSignerType())) {
				TrDocumentDSign docDSign = new TrDocumentDSign();
				docDSign.setUsrCrt(audit.getCallerId());
				docDSign.setDtmCrt(new Date());
				docDSign.setAmMsuser(daoFactory.getUserDao().getUserByIdNo(signer.getIdKtp()));
				docDSign.setTrDocumentD(newDoc);
				docDSign.setSentDate(new Date());
				
				String signActionCode = "at".equals(signer.getSignAction()) ? GlobalVal.CODE_LOV_AUTOSIGN : GlobalVal.CODE_LOV_MANUALSIGN;
				docDSign.setMsLovByLovAutosign(daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN, signActionCode));
				docDSign.setSignLocation(GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode()) ? sl.getSignLocation() : sl.getTekenAjaSignLocation());
				docDSign.setVidaSignLocation(sl.getVidaSignLocation());
				docDSign.setPrivySignLocation(sl.getPrivySignLocation());
				docDSign.setSignPage(sl.getSignPage());
				docDSign.setSeqNo(sl.getSeqNo());
				docDSign.setMsLovByLovSignType(sl.getMsLovByLovSignType());
				docDSign.setMsLovByLovSignerType(sl.getMsLovByLovSignerType());
				if (signer.getSignAction().equalsIgnoreCase(GlobalVal.CODE_DIGISIGN_AUTOSIGN)) {
					if ("1".equals(request.getIsSequence())) {
						throw new DocumentException(messageSource.getMessage(MSG_DOC_CANNOT_AUTOSIGN_WHEN_SEQ, null, this.retrieveLocaleAudit(audit)),
								ReasonDocument.CANNOT_AUTOSIGN);
					}
					
					if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(newDoc.getMsLovByLovPaymentSignType().getCode()) && newDoc.getTotalSigned() == 0) {
						MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_DOC);
						BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balType);
						if (balance.intValue() < 0) {
							throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH,
									new Object[] { balType.getDescription() },
									this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
						}
					} else if (GlobalVal.PAY_SIGN_TYPE_SIGN.equals(newDoc.getMsLovByLovPaymentSignType().getCode())) {
						MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
						BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balType);
						if (balance.intValue() < 0) {
							throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH,
									new Object[] { balType.getDescription() },
									this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
						}
					}
					
					if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
						String trxNo = "";
						try {
							trxNo = this.autosignFullApi(signer, docH, newDoc, docDSign, audit);
						} catch (Exception e) {
							throw new DocumentException(messageSource.getMessage("businesslogic.document.checkautosigndata", null, this.retrieveLocaleAudit(audit)), 
									ReasonDocument.AUTOSIGN_FAILED);
						}
						
						if (null != trxNo) {
							trxNos.add(trxNo);
						}
					} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
						String ipAddress = MssTool.getApplicationIpAddress();
						boolean qrEnable = "1".equals(newDoc.getUseSignQr());
						PoaRequest poaRequest = vidaLogic.buildPoaRequest(signer.getEmail(), signer.getIdKtp(), request.getDocumentFile(), sl.getVidaSignLocation(), sl.getSignPage(), qrEnable, ipAddress, docDSign, tenant, audit);
						if (!(poaRequest.getRequestInfo() == null && poaRequest.getSigner() == null && poaRequest.getSigningInfo() == null)) {
							PoaResponse responsePoa = vidaLogic.doPoa(poaRequest, tenant, newDoc, docH, docDSign, audit);
							if (responsePoa.getErrors() != null || responsePoa.getCode() != 2) {
								throw new DocumentException(CONST_FAIL_POA_VIDA + responsePoa.getErrors()[0].getTitle(), ReasonDocument.AUTOSIGN_FAILED);
							} else {
								request.setDocumentFile(responsePoa.getSignedDocument());
								trxNos.add(responsePoa.getTrxNo());
								docDSign.setPoaId(poaRequest.getSigner().getKeyId());
							}
						}
					} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
//						tidak ada update data
					}
				}
				
				daoFactory.getDocumentDao().insertDocumentDetailSign(docDSign);
				if (!listDocSigner.contains(signer)) {
					listDocSigner.add(signer);
				}
				
				totalSign++;
			}
		}
		
		if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID) && totalSign > 1) {
			throw new DocumentException(messageSource.getMessage(MSG_MAXSIGNEXCEEDED, 
					new Object[] {vendor.getVendorName()}, this.retrieveLocaleAudit(audit)), ReasonDocument.MAX_SIGN_EXCEEDED);
		}
		
		return totalSign;
	}
	
	private int insertDocumentDetailWithoutTemplate(TrDocumentD newDoc, SendDocFullApiSignerBean signer, 
			List<String> trxNos, TrDocumentH docH, SendDocFullApiRequestBean request, List<SendDocFullApiSignerBean> listDocSigner,
			MsVendor vendor, MsTenant tenant, AuditContext audit) throws InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, IOException, InterruptedException {
		int totalSign = 0;
		
		if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID) && signer.getSignLocations().size() > 1) {
			throw new DocumentException(messageSource.getMessage(MSG_MAXSIGNEXCEEDED, 
					new Object[] {vendor.getVendorName()}, this.retrieveLocaleAudit(audit)), ReasonDocument.MAX_SIGN_EXCEEDED);
		}
		
		for (SignLocationFullApiBean sl : signer.getSignLocations()) {
			
			TrDocumentDSign docDSign = new TrDocumentDSign();
			docDSign.setUsrCrt(audit.getCallerId());
			docDSign.setDtmCrt(new Date());
			docDSign.setAmMsuser(daoFactory.getUserDao().getUserByIdNo(signer.getIdKtp()));
			docDSign.setTrDocumentD(newDoc);
			docDSign.setSentDate(new Date());
			
			String signActionCode = "at".equals(signer.getSignAction()) ? GlobalVal.CODE_LOV_AUTOSIGN : GlobalVal.CODE_LOV_MANUALSIGN;
			docDSign.setMsLovByLovAutosign(daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN, signActionCode));
			docDSign.setSignPage(sl.getPage());
			SignLocationBean slb = new SignLocationBean();
			slb.setLlx(sl.getLlx());
			slb.setLly(sl.getLly());
			slb.setUrx(sl.getUrx());
			slb.setUry(sl.getUry());
			SignatureDetailBean vsl = new SignatureDetailBean();
			vsl.setX(Math.round(Double.valueOf(sl.getLlx())));
			vsl.setY(Math.round(Double.valueOf(sl.getLly())));
			vsl.setW(Math.round(Double.valueOf(sl.getUrx()) - Double.valueOf(sl.getLlx())));
			vsl.setH(Math.round(Double.valueOf(sl.getUry()) - Double.valueOf(sl.getLly())));
			
			SignatureDetailBean psl = new SignatureDetailBean();
			psl.setX(Math.round(Double.valueOf(sl.getUrx())));
			psl.setY(Math.round(Double.valueOf(sl.getLly())));
			docDSign.setSignLocation(gson.toJson(slb));
			docDSign.setVidaSignLocation(gson.toJson(vsl).replace(".0", ""));
			docDSign.setPrivySignLocation(gson.toJson(psl).replace(".0", ""));
			docDSign.setSeqNo(signer.getSeqNo());
			
			MsLov signType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_TYPE, GlobalVal.CODE_LOV_SIGN_TYPE_TTD);
			MsLov signerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, StringUtils.upperCase(signer.getSignerType()));
			docDSign.setMsLovByLovSignType(signType);
			docDSign.setMsLovByLovSignerType(signerType);
			if (signer.getSignAction().equalsIgnoreCase(GlobalVal.CODE_DIGISIGN_AUTOSIGN)) {
				if ("1".equals(request.getIsSequence())) {
					throw new DocumentException(messageSource.getMessage(MSG_DOC_CANNOT_AUTOSIGN_WHEN_SEQ, null, this.retrieveLocaleAudit(audit)),
							ReasonDocument.CANNOT_AUTOSIGN);
				}
				
				if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(newDoc.getMsLovByLovPaymentSignType().getCode()) && newDoc.getTotalSigned() == 0) {
					MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_DOC);
					BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balType);
					if (balance.intValue() < 0) {
						throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH,
								new Object[] { balType.getDescription() },
								this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
					}
				} else if (GlobalVal.PAY_SIGN_TYPE_SIGN.equals(newDoc.getMsLovByLovPaymentSignType().getCode())) {
					MsLov balType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
					BigInteger balance = daoFactory.getBalanceMutationDao().getSingleBalanceByVendorAndTenant(tenant, vendor, balType);
					if (balance.intValue() < 0) {
						throw new SaldoException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_SALDO_BALANCE_NOT_ENOUGH,
								new Object[] { balType.getDescription() },
								this.retrieveLocaleAudit(audit)), ReasonSaldo.BALANCE_NOT_ENOUGH);
					}
				}
				
				if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_DIGISIGN)) {
					String trxNo = this.autosignFullApi(signer, docH, newDoc, docDSign, audit);
					if (null != trxNo) {
						trxNos.add(trxNo);
					}
				} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_VIDA)) {
					String ipAddress = MssTool.getApplicationIpAddress();
					boolean qrEnable = "1".equals(newDoc.getUseSignQr());
		
					PoaRequest poaRequest = vidaLogic.buildPoaRequest(signer.getEmail(), signer.getIdKtp(), request.getDocumentFile(), gson.toJson(vsl), sl.getPage(), qrEnable, ipAddress, docDSign, tenant, audit);
						
					if (!(poaRequest.getRequestInfo() == null && poaRequest.getSigner() == null && poaRequest.getSigningInfo() == null)) {
						PoaResponse responsePoa = vidaLogic.doPoa(poaRequest, tenant, newDoc, docH, docDSign, audit);
						if (responsePoa.getErrors() != null || responsePoa.getCode() != 2) {
							throw new DocumentException(CONST_FAIL_POA_VIDA + responsePoa.getErrors()[0].getTitle(), 
								ReasonDocument.AUTOSIGN_FAILED);
						} else {
							request.setDocumentFile(responsePoa.getSignedDocument());
							trxNos.add(responsePoa.getTrxNo());
							docDSign.setPoaId(poaRequest.getSigner().getKeyId());
						}
					}
				} else if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_PRIVY_ID)) {
//					bisa autosign tanpa template, tidak update data apapun
				}
			}
			daoFactory.getDocumentDao().insertDocumentDetailSign(docDSign);
			if (!listDocSigner.contains(signer)) {
				listDocSigner.add(signer);
			}
			
			totalSign++;
		}
		
		return totalSign;
	}
	
	private short insertTrDocumentDStampduty(List<MsDocTemplateSignLoc> signLocs, TrDocumentD docD, AuditContext audit) {
		short count = 0;
		for (MsDocTemplateSignLoc sl : signLocs) {
			if (sl.getMsLovByLovSignType().getCode().equalsIgnoreCase(GlobalVal.CODE_LOV_SIGN_TYPE_SDT)) {
				TrDocumentDStampduty sdt = new TrDocumentDStampduty();
				sdt.setTrDocumentD(docD);
				sdt.setDtmCrt(new Date());
				sdt.setUsrCrt(audit.getCallerId());
				sdt.setSignLocation(sl.getSignLocation());
				sdt.setSignPage(sl.getSignPage());
				sdt.setTransform(sl.getTransform());
				sdt.setSeqNo(sl.getSeqNo());
				sdt.setPrivySignLocation(sl.getPrivySignLocation());
				daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);
				count++;
			}
		}
		return count;
	}
	
	private short insertTrDocumentDStampdutyFullApi(List<SignLocationFullApiBean> signLocs, TrDocumentD docD, AuditContext audit) {
		short count = 0;
		for (SignLocationFullApiBean sl : signLocs) {
			SignLocationBean slb = new SignLocationBean();
			slb.setLlx(sl.getLlx());
			slb.setLly(sl.getLly());
			slb.setUrx(sl.getUrx());
			slb.setUry(sl.getUry());
			
			SignatureDetailBean psl = new SignatureDetailBean();
			psl.setX(Math.round(Double.valueOf(sl.getUrx())));
			psl.setY(Math.round(Double.valueOf(sl.getLly())));
			
			TrDocumentDStampduty sdt = new TrDocumentDStampduty();
			sdt.setTrDocumentD(docD);
			sdt.setDtmCrt(new Date());
			sdt.setUsrCrt(audit.getCallerId());
			sdt.setSignLocation(gson.toJson(slb));
			sdt.setSignPage(sl.getPage());
			sdt.setPrivySignLocation(gson.toJson(psl).replace(".0", ""));
			daoFactory.getDocumentDao().insertDocumentDetailSdt(sdt);
			count++;
		}
		
		return count;
	}
	
	private void autosign(SignerBean bean, TrDocumentH docH, TrDocumentD docD, TrDocumentDSign docDSign, MsOffice office, MsBusinessLine businessLine, AuditContext audit) {
		/*
		 * Code review Axel 2023-05-04
		 * getUserByLoginId diganti validateGetUserByEmailv2 untuk kasus multi PSrE
		 */
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(bean.getEmail(), false, audit);
		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(bean.getEmail(), docD.getMsVendor().getVendorCode());

		if (!bean.getSignerType().contains(GlobalVal.CODE_LOV_SIGNER_TYPE_MF)  || null == vRUser.getVendorUserAutosignKey() ) {
			throw new UserException(this.messageSource.getMessage("businesslogic.document.customernotallowed", null,
					this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_USER_ROLE);
		}
				
		if (user == null ||  !"1".equals(vRUser.getIsActive()) || StringUtils.isBlank(vRUser.getVendorUserAutosignKey())) {
			bean.setSignAction(GlobalVal.CODE_DIGISIGN_MANUALSIGN);
			MsLov autosign = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN, GlobalVal.CODE_LOV_MANUALSIGN);
			docDSign.setMsLovByLovAutosign(autosign);
		} else {
			docDSign.setSignDate(new Date());
			
			MsLov balanceType = new MsLov();
			MsLov trxType = new MsLov();
			
			if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(docD.getMsLovByLovPaymentSignType().getCode()) && docD.getTotalSigned() == 0) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_DOC);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UDOC);			
			
			} else if(GlobalVal.PAY_SIGN_TYPE_SIGN.equals(docD.getMsLovByLovPaymentSignType().getCode())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USGN);
			
			}
			
			docD.setTotalSigned((short) (docD.getTotalSigned() + 1));
			
			// Skip proses insert ke balance mutation jika payment type DOC dan autosign bukan yang pertama kali
			if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(docD.getMsLovByLovPaymentSignType().getCode()) && docD.getTotalSigned() > 1) {
				return;
			}
			
			Date trxDate = new Date();
			int qty = -1;
			long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();

			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, docD.getMsTenant(), docD.getMsVendor(), trxDate,
					StringUtils.upperCase(docH.getRefNumber()), qty, String.valueOf(nextTrxNo),  user, null, null, office, businessLine, audit);
			
		}
		
	}
	
	private String autosignFullApi(SendDocFullApiSignerBean bean, TrDocumentH docH, TrDocumentD docD, TrDocumentDSign docDSign, AuditContext audit) {
		/*
		 * Code review Axel 2023-05-04
		 * getUserByLoginId diganti validateGetUserByEmailv2 untuk kasus multi PSrE
		 */
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(bean.getEmail(), false, audit);
		MsVendorRegisteredUser vRUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByLoginIdAndVendorCode(bean.getEmail(), docD.getMsVendor().getVendorCode());

		if (!bean.getSignerType().contains(GlobalVal.CODE_LOV_SIGNER_TYPE_MF)  || null == vRUser.getVendorUserAutosignKey() ) {
			throw new UserException(this.messageSource.getMessage("businesslogic.document.customernotallowed", null,
					this.retrieveLocaleAudit(audit)), ReasonUser.INVALID_USER_ROLE);
		}
				
		if (user == null ||  !"1".equals(vRUser.getIsActive()) || StringUtils.isBlank(vRUser.getVendorUserAutosignKey())) {
			bean.setSignAction(GlobalVal.CODE_DIGISIGN_MANUALSIGN);
			MsLov autosign = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_AUTOSIGN, GlobalVal.CODE_LOV_MANUALSIGN);
			docDSign.setMsLovByLovAutosign(autosign);
			return null;
		} else {
			docDSign.setSignDate(new Date());
			
			MsLov balanceType = new MsLov();
			MsLov trxType = new MsLov();
			
			if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(docD.getMsLovByLovPaymentSignType().getCode()) && docD.getTotalSigned() == 0) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_DOC);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UDOC);			
			
			} else if(GlobalVal.PAY_SIGN_TYPE_SIGN.equals(docD.getMsLovByLovPaymentSignType().getCode())) {
				balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SGN);
				trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USGN);
			
			}
			
			docD.setTotalSigned((short) (docD.getTotalSigned() + 1));
			
			// Skip proses insert ke balance mutation jika payment type DOC dan autosign bukan yang pertama kali
			if (GlobalVal.PAY_SIGN_TYPE_DOC.equals(docD.getMsLovByLovPaymentSignType().getCode()) && docD.getTotalSigned() > 1) {
				return null;
			}
			
			Date trxDate = new Date();
			int qty = -1;
			long nextTrxNo = daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo();

			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, docD.getMsTenant(), docD.getMsVendor(), trxDate,
					StringUtils.upperCase(docH.getRefNumber()), qty, String.valueOf(nextTrxNo),  user, null, null, docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			
			return String.valueOf(nextTrxNo);
		}
		
	}
	
	private boolean isSameFile(String prevFile, String currFile) {
		byte[] prev = Base64.getDecoder().decode(prevFile);
		byte[] curr = Base64.getDecoder().decode(currFile);
		
		return prev == curr;
	}
	
	private String sendDocumentOneByOne(MsTenant tenant, MsVendor vendor, DocumentConfinsRequestBean documentConfinsRequest, 
			TrDocumentH docH, String[]  mapNewUserPassword, List<Map<String, Object>> userEmailList, AuditContext audit) throws IOException, InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InterruptedException {
		boolean isAllRegistered = true;
		if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			isAllRegistered = this.isAllRegistered(documentConfinsRequest.getSigner());
		}
		
		MsNotificationtypeoftenant ntt = daoFactory.getNotificationtypeoftenantDao().getNotificationType(tenant, NotificationSendingPoint.SEND_DOC.getLovCode());
		
		String mustUseWaFirst = null == ntt ? tenant.getMustUseWaFirst() : ntt.getMustUseWaFirst();
		String useWaMessage = null == ntt ? tenant.getUseWaMessage() : ntt.getUseWaMessage();
		
		if ("1".equals(mustUseWaFirst) || "1".equals(useWaMessage)) {
			checkWaBalance(ntt, tenant, userEmailList, vendor, audit);
		}
		
		String[] signerPassword = new String[mapNewUserPassword.length];
		SignerBean[] docSigner = new SignerBean[documentConfinsRequest.getSigner().length];
		TrDocumentD docD = insertDocumentConfins(documentConfinsRequest, tenant, vendor, docH, docSigner, signerPassword, mapNewUserPassword, audit);
		String documentId = docD.getDocumentId();
		documentConfinsRequest.setDocumentId(documentId);
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			try {
					digisignLogic.sendDocumentDigisign(documentConfinsRequest, null, documentId, tenant, vendor, audit);
			} catch (IOException e) { 
				throw new DigisignException(this.messageSource.getMessage(MSG_ERRORSEND,
						new Object[] {"Digisign", ExceptionUtils.getStackTrace(e)}, this.retrieveLocaleAudit(audit)));
			}
		} else if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			if (isAllRegistered) {
				try {
					this.uploadDocToTknaj(docD, documentConfinsRequest, null, tenant, vendor, docH, audit);
				} catch (IOException e) {
					throw new TekenajaException(this.messageSource.getMessage(MSG_ERRORSEND,
							new Object[] {TEKEN_AJA, ExceptionUtils.getStackTrace(e)}, this.retrieveLocaleAudit(audit)));
				}
			}
			
			byte[] dataPdfDocument = Base64.getDecoder().decode(documentConfinsRequest.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
		} else if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			byte[] dataPdfDocument = Base64.getDecoder().decode(documentConfinsRequest.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
			
		} else if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			PrivyGeneralUploadDocumentResponse uploadResp = privyGeneralLogic.uploadDoc(documentConfinsRequest, null, null, docD, tenant, vendor, audit);
			if (StringUtils.isBlank(uploadResp.getReferenceNumber()) && null == uploadResp.getError()) {
				docD.setPsreDocumentId(uploadResp.getData().getDocumentToken());
				daoFactory.getDocumentDao().updateDocumentDetail(docD);
			} else {
				String message = privyGeneralLogic.buildUploadDocErrorMessage(uploadResp, audit);
				throw new DocumentException(message, ReasonDocument.UPLOAD_DOC_FAILED);
			}
			
			byte[] dataPdfDocument = Base64.getDecoder().decode(documentConfinsRequest.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
		}
		
		List<Map<String, Object>> listEmail = new ArrayList<>();
		SignerBean[] signerEmail = new SignerBean[documentConfinsRequest.getSigner().length];
		String[] passwordEmail = new String[mapNewUserPassword.length];
		
		List<Map<String, Object>> listSms = new ArrayList<>();
		String[] passwordSms = new String[mapNewUserPassword.length];
		SignerBean[] signerSms = new SignerBean[documentConfinsRequest.getSigner().length];
		
		List<String> toSend = new ArrayList<>();
		for (int i = 0; i < userEmailList.size(); i++) {
			String email = (String) userEmailList.get(i).get(MAP_KEY_EMAIL);
			toSend.add(StringUtils.upperCase(email));
		}
		
		int signerCount = 0;
		int signerSmsCount = 0;
		int signerEmailCount = 0;
		NotificationType phoneNotifType = NotificationType.SMS_VFIRST;
		for (SignerBean email : docSigner) {
			if (null != email  && toSend.stream().anyMatch(email.getEmail()::equalsIgnoreCase)) {
				NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, email.getEmailService());
				if (type.compareTo(NotificationType.EMAIL) != 0) {
					phoneNotifType = type;
					for (Map<String, Object> signer : userEmailList) {
						if (email.getEmail().equalsIgnoreCase((String) signer.get(MAP_KEY_EMAIL)) && !listSms.contains(signer)){
							listSms.add(signer);
						}
					}
					passwordSms[signerSmsCount] = signerPassword[signerCount];
					signerSms[signerSmsCount] = email;
					signerSmsCount++;
				} else {
					for (Map<String, Object> signer : userEmailList) {
						if (email.getEmail().equalsIgnoreCase((String) signer.get(MAP_KEY_EMAIL)) && !listEmail.contains(signer)){
							listEmail.add(signer);
						}
					}
					signerEmail[signerEmailCount] = email;
					passwordEmail[signerEmailCount] = signerPassword[signerCount];
					signerEmailCount++;
				}
				
				signerCount++;
			}
		}
		
		// Send email sign request
		if (!listEmail.isEmpty()){
			sendSignRequestEmail(signerEmail, documentId, passwordEmail, listEmail, userEmailList, tenant, vendor);
		}
		
		if(!listSms.isEmpty()){
			if (phoneNotifType.compareTo(NotificationType.WHATSAPP) == 0 || phoneNotifType.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
				sendSignRequestWa(signerSms, docH, docD, passwordSms, listSms, userEmailList, tenant, phoneNotifType, audit);
			} else {
				sendSignRequestSms(signerSms, docH, docD, passwordSms, listSms, userEmailList, tenant, vendor, phoneNotifType, audit);	
			}
		}
		
		insertAuditTrailDetail(userEmailList, docSigner, docD, audit);
		
		return documentId;
	}
	
	private void insertAuditTrailDetail(List<Map<String, Object>> userEmailList, SignerBean[] docSigner, TrDocumentD docD, AuditContext audit) {
		for (SignerBean signer : docSigner) {
			for (Map<String, Object> user : userEmailList) {
				TrSigningProcessAuditTrail auditTrail = (TrSigningProcessAuditTrail) user.get(MAP_KEY_AUDIT_TRAIL);
				if (null != signer && signer.getEmail().equalsIgnoreCase((String) user.get(MAP_KEY_EMAIL))) {
					TrSigningProcessAuditTrailDetail detail = new TrSigningProcessAuditTrailDetail();
					detail.setUsrCrt(audit.getCallerId());
					detail.setDtmCrt(new Date());
					detail.setSigningProcessAuditTrail(auditTrail);
					detail.setTrDocumentD(docD);
					daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetail(detail);
				}
			}
		}
	}
	
	private void sendSignRequestSms(SignerBean[] signerArr, TrDocumentH docH, TrDocumentD docD, String[] password, List<Map<String, Object>> listSms, List<Map<String, Object>> userEmailListAll, MsTenant tenant, MsVendor vendor, NotificationType type, AuditContext audit) {
		String link;
		int numOfSms = listSms.size();
		
		for(int i = 0; i < numOfSms; i++) {
			link = generateSignLink(docD.getDocumentId());
			Map<String, Object> userMap = new HashMap<>();
			userMap.put("link", link);
			userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
			if (null != password) {
				userMap.put(MAP_KEY_PASSWORD, password[i]);
			}
			
			Map<String, Object> param = new HashMap<>();
			param.put("user", userMap);
			
			
			if (!isEmailSent(userEmailListAll, signerArr[i].getEmail())) {
				boolean isFirstTimeUser = password != null && StringUtils.isNotBlank(password[i]);
				String pass = null != password ? password[i] : null;
				LOG.info("Sending signature request SMS to: {}, Password: {}, first time user = {}",
						 signerArr[i].getUserPhone(), pass, isFirstTimeUser);
				
				MsMsgTemplate msgTemplate;
				if (isFirstTimeUser) {
					msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS_1ST, param);
				} else {
					msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS, param);
				}
				
				sendSms(userEmailListAll, signerArr[i], msgTemplate, tenant, vendor, docH, docD, type, audit);
			}
		}
	}
	
	private void sendSms(List<Map<String, Object>> userEmailListAll, SignerBean signer, MsMsgTemplate msgTemplate, MsTenant tenant, MsVendor vendor, TrDocumentH docH, TrDocumentD docD, NotificationType type, AuditContext audit) {
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signer.getEmail(), false, audit);
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		AmGeneralsetting gs = daoFactory.getGeneralSettingDao().getGsObjByCode(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		
		if (type.compareTo(NotificationType.SMS_JATIS) == 0) {
			if ("1".equals(gs.getGsValue())) {
				JatisSmsRequestBean jatisReq = new JatisSmsRequestBean(tenant, signer.getUserPhone(), msgTemplate.getBody(), trxNo, false);
				sendSmsJatis(jatisReq, signer.getEmail(), userEmailListAll, docH, docD, user, tenant, audit);
			} else {
				MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
				MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
				MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
				String notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, signer.getUserPhone());
				MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
				
				saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), -1, trxNo, user, notes, DUMMY_TRX_NO, docH.getMsOffice(), docH.getMsBusinessLine(), audit);
				messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg , trxNo, DUMMY_TRX_NO,
						signer.getUserPhone(), NotificationType.SMS_JATIS, gateway, sendingPoint, audit);
				LOG.info("Sign Request sent via SMS with JATIS to : {}", signer.getUserPhone());
			}
			
		} else {
			if ("1".equals(gs.getGsValue())) {
				sendSmsVFirst(userEmailListAll, signer, msgTemplate, tenant, vendor, docH, docD, audit);
			} else {
				MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
				MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
				MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
				MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
				String notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, signer.getUserPhone());
				MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);

				saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), -1, trxNo, user, notes, DUMMY_TRX_NO, docH.getMsOffice(), docH.getMsBusinessLine(), audit);
				messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, trxNo, DUMMY_TRX_NO,
						signer.getUserPhone(), NotificationType.SMS_VFIRST, gateway, sendingPoint, audit);
				LOG.info("Sign Request sent via SMS with VFirst to : {}", signer.getUserPhone());
			}
		}
	}
	
	private SendDocFullApiResponseBean sendDocumentOneByOneFullApi(MsTenant tenant, MsVendor vendor, SendDocFullApiRequestBean request, 
			TrDocumentH docH, List<Map<String, Object>> userEmailList, AuditContext audit) throws IOException, InvalidKeyException, InvalidAlgorithmParameterException, NoSuchPaddingException, IllegalBlockSizeException, NoSuchAlgorithmException, BadPaddingException, InterruptedException {
		SignerBean[] docSigner = new SignerBean[request.getSigners().size()];
		
		boolean isAllRegistered = true;
		if (vendor.getVendorCode().equals(GlobalVal.VENDOR_CODE_TEKENAJA)) {
			isAllRegistered = this.isAllRegistered(docSigner);
		}
		
		SendDocFullApiResponseBean response = new SendDocFullApiResponseBean();
		TrDocumentD docD = insertDocumentDetailFullApi(request,tenant, vendor, docH, docSigner, response, audit);
		String documentId = docD.getDocumentId();
		
		DocumentConfinsRequestBean confinsRequest = convertRequestToConfinsRequest(request, documentId, vendor.getVendorCode(), docSigner);
		
		if (GlobalVal.VENDOR_CODE_DIGISIGN.equals(vendor.getVendorCode())) {
			try {
					digisignLogic.sendDocumentDigisign(confinsRequest, null, documentId, tenant, vendor, audit);
			} catch (IOException e) { 
				throw new DigisignException(this.messageSource.getMessage(MSG_ERRORSEND,
						new Object[] {"Digisign", ExceptionUtils.getStackTrace(e)}, this.retrieveLocaleAudit(audit)));
			}
		} else if (GlobalVal.VENDOR_CODE_TEKENAJA.equals(vendor.getVendorCode())) {
			if (isAllRegistered) {
				try {
					this.uploadDocToTknaj(docD, confinsRequest, null, tenant, vendor, docH, audit);
				} catch (IOException e) {
					throw new TekenajaException(this.messageSource.getMessage(MSG_ERRORSEND,
							new Object[] {TEKEN_AJA, ExceptionUtils.getStackTrace(e)}, this.retrieveLocaleAudit(audit)));
				}
			}
			
			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeDocument(request.getReferenceNo(), docD.getDocumentId(), dataPdfDocument);
		}else if (GlobalVal.VENDOR_CODE_VIDA.equals(vendor.getVendorCode())) {
			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
			
		} else if (GlobalVal.VENDOR_CODE_PRIVY_ID.equals(vendor.getVendorCode())) {
			PrivyGeneralUploadDocumentResponse uploadResp = privyGeneralLogic.uploadDoc(null, request, null, docD, tenant, vendor, audit);
			if (StringUtils.isNotBlank(uploadResp.getReferenceNumber()) || null == uploadResp.getError()) {
				docD.setPsreDocumentId(uploadResp.getData().getDocumentToken());
				daoFactory.getDocumentDao().updateDocumentDetail(docD);
			} else {
				String message = privyGeneralLogic.buildUploadDocErrorMessage(uploadResp, audit);
				throw new DocumentException(message, ReasonDocument.UPLOAD_DOC_FAILED);
			}
			
			byte[] dataPdfDocument = Base64.getDecoder().decode(request.getDocumentFile());
			cloudStorageLogic.storeBaseSignDocument(docD, dataPdfDocument);
		}
		
		if ("1".equals(tenant.getSignRequestNotification())) {
			List<Map<String, Object>> listEmail = new ArrayList<>();
			SignerBean[] signerEmail = new SignerBean[request.getSigners().size()];
			
			List<Map<String, Object>> listSms = new ArrayList<>();
			SignerBean[] signerSms = new SignerBean[request.getSigners().size()];
			
			List<String> toSend = new ArrayList<>();
			for (int i = 0; i < userEmailList.size(); i++) {
				String email = (String) userEmailList.get(i).get(MAP_KEY_EMAIL);
				toSend.add(StringUtils.upperCase(email));
			}
			
			int signerSmsCount = 0;
			int signerEmailCount = 0;
			NotificationType phoneNotifType = NotificationType.SMS_VFIRST;
			for (SignerBean email : docSigner) {
				if (null != email  && toSend.stream().anyMatch(email.getEmail()::equalsIgnoreCase)) {
					NotificationType type = tenantLogic.getNotificationType(tenant, NotificationSendingPoint.SEND_DOC, email.getEmailService());
					if (type.compareTo(NotificationType.EMAIL) != 0) {
						phoneNotifType = type;
						for (Map<String, Object> signer : userEmailList) {
							if (email.getEmail().equalsIgnoreCase((String) signer.get(MAP_KEY_EMAIL)) && !listSms.contains(signer)){
								listSms.add(signer);
							}
						}
						signerSms[signerSmsCount] = email;
						signerSmsCount++;
					} else {
						for (Map<String, Object> signer : userEmailList) {
							if (email.getEmail().equalsIgnoreCase((String) signer.get(MAP_KEY_EMAIL)) && !listEmail.contains(signer)){
								listEmail.add(signer);
							}
						}
						signerEmail[signerEmailCount] = email;
						signerEmailCount++;
					}
				}
			}
			
			// Send email sign request
			if (!listEmail.isEmpty()) {
				sendSignRequestEmail(signerEmail, documentId, null, listEmail, userEmailList, tenant, vendor);
			}
			
			if(!listSms.isEmpty()){
				if (phoneNotifType.compareTo(NotificationType.WHATSAPP) == 0 || phoneNotifType.compareTo(NotificationType.WHATSAPP_HALOSIS) == 0) {
					sendSignRequestWa(signerSms, docH, docD, null, listSms, userEmailList, tenant, phoneNotifType, audit);
				} else {
					sendSignRequestSmsFullApi(signerSms, docH, docD, null, listSms, userEmailList, tenant, vendor, audit);
				}
			}
		} else {
			for (SignerBean email : docSigner) {
				insertAuditTrailHeader(userEmailList, email.getEmail(), true);
			}
		}
		
		insertAuditTrailDetail(userEmailList, docSigner, docD, audit);
		
		response.setDocTemplateCode(request.getDocumentTemplateCode());
		response.setPsreCode(vendor.getVendorCode());
		
		return response;
	}
	
	private DocumentConfinsRequestBean convertRequestToConfinsRequest(SendDocFullApiRequestBean fullApiReq, String documentId, String vendorCode, SignerBean[] signer) {
		DocumentConfinsRequestBean confinsReq = new DocumentConfinsRequestBean();
		confinsReq.setBusinessLineCode(fullApiReq.getBusinessLineCode());
		confinsReq.setBusinessLineName(fullApiReq.getBusinessLineName());
		confinsReq.setDocumentFile(fullApiReq.getDocumentFile());
		confinsReq.setDocumentId(documentId);
		confinsReq.setDocumentTemplateCode(fullApiReq.getDocumentTemplateCode());
		confinsReq.setIsSequence(fullApiReq.getIsSequence());
		confinsReq.setOfficeCode(fullApiReq.getOfficeCode());
		confinsReq.setOfficeName(fullApiReq.getOfficeName());
		confinsReq.setReferenceNo(fullApiReq.getReferenceNo());
		confinsReq.setRegionCode(fullApiReq.getRegionCode());
		confinsReq.setRegionName(fullApiReq.getRegionName());
		confinsReq.setSigner(signer);
		
		return confinsReq;
	}
	
	private void uploadDocToTknaj(TrDocumentD docD, DocumentConfinsRequestBean documentConfinsRequest, InsertDocumentManualSignRequest manualSignReq, MsTenant tenant, MsVendor vendor, TrDocumentH docH, AuditContext audit) throws IOException {
		TknajUplDocResponse response = tekenajaLogic.uploadDoc(documentConfinsRequest, manualSignReq, docD.getDocumentId(), vendor, tenant, docD, docH, audit);
		
		if (!"OK".equals(response.getStatus())) {
			throw new TekenajaException(this.messageSource.getMessage(MSG_ERRORSEND,
					new Object[] {TEKEN_AJA, response.getMessage()}, this.retrieveLocaleAudit(audit)));
		}
		
		docD.setSendStatus((short) 3);
		docD.setPsreDocumentId(response.getData().getId());
		daoFactory.getDocumentDao().updateDocumentDetail(docD);
	}
	
	private boolean isAllRegistered(SignerBean[] signer) {
		for (SignerBean bean : signer) {
			if ("0".equals(bean.getIsActive())) {
				return false;
			}
		}
		
		return true;
	}
	
	private void checkWaBalance(MsNotificationtypeoftenant ntt, MsTenant tenant, List<Map<String, Object>> userEmailList, MsVendor vendor, AuditContext audit) {
		String mustUseWaFirst = null == ntt ? tenant.getMustUseWaFirst() : ntt.getMustUseWaFirst();
		String useWaMessage = null == ntt ? tenant.getUseWaMessage() : ntt.getUseWaMessage();
		List<String> phones = new ArrayList<>();
		if ("1".equals(mustUseWaFirst)) {
			for (Map<String, Object> user : userEmailList) {
				phones.add((String) user.get(MAP_KEY_PHONE));
			}
		} else if ("1".equals(useWaMessage)) {
			for (Map<String, Object> user : userEmailList) {
				String email = (String) user.get(MAP_KEY_EMAIL);
				String notifTypeString = tenantLogic.checkNotificationType(tenant.getTenantCode(), email, vendor.getVendorCode(), audit);
				if (GlobalVal.NOTIF_TYPE_SMS.equals(notifTypeString)) {
					phones.add((String) user.get(MAP_KEY_PHONE));
				}
			}
		}
		
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		if ("1".equals(gs.getGsValue())) {
			balanceValidatorLogic.validateWhatsAppNotifBalanceAvailabilityWithAmount(tenant, phones, audit);
		}
	}
	
	private void sendSignRequestWa(SignerBean[] signerArr, TrDocumentH docH, TrDocumentD docD, String[] password, List<Map<String, Object>> listSms, List<Map<String, Object>> userEmailListAll, MsTenant tenant, NotificationType notifType, AuditContext audit) {
		String link = null;
		int numOfWa = listSms.size();
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_WA_SENDDOC);
		
		Map<String, Object> docMap = new HashMap<>();
		docMap.put("refNumber", docH.getRefNumber());
		
		Map<String, Object> param = new HashMap<>();
		param.put("doc", docMap);
		
		for (int i = 0; i < numOfWa; i++) {
			if (StringUtils.isEmpty(link)) {
				link = generateSignLink(docD.getDocumentId());
			}
			
			if (!isEmailSent(userEmailListAll, signerArr[i].getEmail())) {
				boolean isFirstTimeUser = password != null && StringUtils.isNotBlank(password[i]);
				String pass = null != password ? password[i] : null;
				LOG.info("Sending signature request WA to: {}, Password: {}, first time user = {}",
						 signerArr[i].getUserPhone(), pass, isFirstTimeUser);
				
				MsMsgTemplate msgTemplate;
				String buttonText = docD.getDocumentId();
				List<String> bodyTexts = new ArrayList<>();
				bodyTexts.add(tenant.getTenantName());
				bodyTexts.add(docD.getDocumentId());
				
				if (isFirstTimeUser) {
					msgTemplate = msgTemplateLogic.getAndParseContent("sign_link_invitation_with_password", param);
					bodyTexts.add(password[i]);
				} else {
					msgTemplate = msgTemplateLogic.getAndParseContent("sign_link_invitation_without_password", param);
				}
				
				List<String> headerTexts = new ArrayList<>();
				headerTexts.add(msgTemplate.getSubject());
				
				AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signerArr[i].getEmail(), false, audit);
				
				String reservedTrxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
				
				if (notifType.compareTo(NotificationType.WHATSAPP) == 0) {
					SendWhatsAppRequest request = new SendWhatsAppRequest();
					request.setBodyTexts(bodyTexts);
					request.setButtonText(buttonText);
					request.setMsTenant(tenant);
					request.setTemplate(msgTemplate);
					request.setAmMsuser(user);
					request.setReservedTrxNo(reservedTrxNo);
					request.setPhoneNumber(signerArr[i].getUserPhone());
					request.setMsBusinessLine(docH.getMsBusinessLine());
					request.setMsOffice(docH.getMsOffice());
					request.setTrDocumentH(docH);
					request.setNotes(String.format(RESEND_SIGN_REQ_WA_NOTES_FORMAT, request.getPhoneNumber()));
					
					boolean isSuccess = false;
					if ("1".equals(gs.getGsValue())) {
						isSuccess = whatsAppLogic.sendMessageNotAsync(request, audit);
					} else {
						String recipient = request.getPhoneNumber();
								
						String balanceCode = request.isOtp() ? GlobalVal.CODE_LOV_BALANCE_TYPE_WA_OTP : GlobalVal.CODE_LOV_BALANCE_TYPE_WA;
						MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_BALANCE_TYPE, balanceCode);
						MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCodeNewTrx(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
						MsVendor vendor = daoFactory.getVendorDao().getVendorByCodeNewTrx(GlobalVal.VENDOR_CODE_ESG);
						MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP);
						MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);

						TrBalanceMutation balanceMutation = new TrBalanceMutation();
						balanceMutation.setTrxNo(reservedTrxNo);
						balanceMutation.setTrxDate(new Date());	
						balanceMutation.setQty(-1);
						balanceMutation.setMsLovByLovBalanceType(balanceType);
						balanceMutation.setMsLovByLovTrxType(trxType);
						balanceMutation.setUsrCrt(StringUtils.left(audit.getCallerId(), 36));
						balanceMutation.setDtmCrt(new Date());
						balanceMutation.setMsTenant(request.getMsTenant());
						balanceMutation.setMsVendor(vendor);
						if (null != request.getAmMsuser()) {
							balanceMutation.setAmMsuser(request.getAmMsuser());
						}
						if (null != request.getTrDocumentH()) {
							balanceMutation.setTrDocumentH(request.getTrDocumentH());
							balanceMutation.setRefNo(request.getTrDocumentH().getRefNumber());
						}
						if (null != request.getMsBusinessLine()) {
							balanceMutation.setMsBusinessLine(request.getMsBusinessLine());
						}
						if (null != request.getMsOffice()) {
							balanceMutation.setMsOffice(request.getMsOffice());
						}
						if (StringUtils.isNotBlank(request.getRefNo()) && StringUtils.isBlank(balanceMutation.getRefNo())) {
							balanceMutation.setRefNo(request.getRefNo());
						}
						String notes = null;
						if (StringUtils.isBlank(request.getNotes())) {
							notes = SEND_WA_TO_NOTE + recipient;
						} else {
							notes = request.getNotes();
						}
						balanceMutation.setNotes(notes);
						balanceMutation.setQty(0);
						balanceMutation.setUsrUpd(audit.getCallerId());
						balanceMutation.setDtmUpd(new Date());
						daoFactory.getBalanceMutationDao().insertTrBalanceMutation(balanceMutation);
						
						messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, reservedTrxNo, null, recipient, NotificationType.WHATSAPP, messageGateway, sendingPoint, audit);

						LOG.info("WA Send Document Sent.");
						isSuccess = true;
					}
					
					insertAuditTrailHeader(userEmailListAll, signerArr[i].getEmail(), isSuccess);
				} else {
					HalosisSendWhatsAppRequestBean request = new HalosisSendWhatsAppRequestBean();
					request.setHeaderTexts(headerTexts);
					request.setBodyTexts(bodyTexts);
					request.setButtonText(null);
					request.setMsTenant(tenant);
					request.setTemplate(msgTemplate);
					request.setAmMsuser(user);
					request.setReservedTrxNo(reservedTrxNo);
					request.setPhoneNumber(signerArr[i].getUserPhone());
					request.setMsBusinessLine(docH.getMsBusinessLine());
					request.setMsOffice(docH.getMsOffice());
					request.setTrDocumentH(docH);
					request.setMsTenant(tenant);
					request.setNotes(String.format(RESEND_SIGN_REQ_WA_NOTES_FORMAT, request.getPhoneNumber()));
					
					boolean isSuccess = false;
					if ("1".equals(gs.getGsValue())) {
						isSuccess = whatsAppHalosisLogic.sendSynchronousMessage(request, audit);
					} else {
						String phone = request.getPhoneNumber();
						MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_WA);
						MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_UWA);
						MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
						MsVendor vendor = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
						MsLov messageGateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_WA_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_WHATSAPP_HALOSIS);

						TrBalanceMutation mutation = new TrBalanceMutation();
						mutation.setTrxNo(request.getReservedTrxNo());
						mutation.setVendorTrxNo(DUMMY_TRX_NO);
						mutation.setTrxDate(new Date());
						mutation.setQty(-1);
						mutation.setMsLovByLovBalanceType(balanceType);
						mutation.setMsLovByLovTrxType(trxType);
						mutation.setUsrCrt(audit.getCallerId());
						mutation.setDtmCrt(new Date());
						mutation.setMsTenant(tenant);
						mutation.setMsVendor(vendor);
						
						if (null != request.getAmMsuser()) {
							mutation.setAmMsuser(request.getAmMsuser());
						}
						if (null != request.getTrDocumentH()) {
							mutation.setTrDocumentH(request.getTrDocumentH());
							mutation.setRefNo(request.getTrDocumentH().getRefNumber());
						}
						if (null != request.getMsBusinessLine()) {
							mutation.setMsBusinessLine(request.getMsBusinessLine());
						}
						if (null != request.getMsOffice()) {
							mutation.setMsOffice(request.getMsOffice());
						}
						if (StringUtils.isBlank(mutation.getRefNo()) && StringUtils.isNotBlank(request.getRefNo())) {
							mutation.setRefNo(request.getRefNo());
						}
						String notes = null;
						if (StringUtils.isBlank(request.getNotes())) {
							notes = SEND_WA_TO_NOTE + phone;
						} else {
							notes = request.getNotes();
						}
						mutation.setNotes(notes);
						daoFactory.getBalanceMutationDao().insertTrBalanceMutation(mutation);

						messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendor, request.getReservedTrxNo(), DUMMY_TRX_NO,
								phone, NotificationType.WHATSAPP_HALOSIS, messageGateway, sendingPoint, audit);
						
						LOG.info("WA Send Document Sent.");
						isSuccess = true;
					}
					
					insertAuditTrailHeader(userEmailListAll, signerArr[i].getEmail(), isSuccess);
				}
			}
		}
	}
	
	private void sendSignRequestSmsFullApi(SignerBean[] signerArr, TrDocumentH docH, TrDocumentD docD, String[] password, List<Map<String, Object>> listSms, List<Map<String, Object>> userEmailListAll, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		String link;
		int numOfSms = listSms.size();
		AmGeneralsetting gs = daoFactory.getCommonDao().getGeneralSetting(AmGlobalKey.GENERALSETTING_SEND_SMS_SENDDOC);
		
		for(int i = 0; i < numOfSms; i++) {
			link = generateSignLink(docD.getDocumentId());
			Map<String, Object> userMap = new HashMap<>();
			userMap.put("link", link);
			userMap.put(MAP_KEY_TENANT, tenant.getTenantName());
			if (null != password) {
				userMap.put(MAP_KEY_PASSWORD, password[i]);
			}
			
			Map<String, Object> param = new HashMap<>();
			param.put("user", userMap);
			
			
			if (!isEmailSent(userEmailListAll, signerArr[i].getEmail())) {
				boolean isFirstTimeUser = null != password && StringUtils.isNotBlank(password[i]);
				String pass = null != password ? password[i] : null;
				LOG.info("Sending signature request SMS to: {}, Password: {}, first time user = {}",
						 signerArr[i].getUserPhone(), pass, isFirstTimeUser);
				
				MsMsgTemplate msgTemplate;
				if (isFirstTimeUser) {
					msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS_1ST, param);
				} else {
					msgTemplate = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_USER_TTD_SMS, param);
				}
				
				if ("1".equals(gs.getGsValue())) {
					sendSmsFullApi(userEmailListAll, signerArr[i], msgTemplate, tenant, vendor, docH, docD, audit);
				} else {
					LOG.info("SMS Send Document Sent to : {}", signerArr[i].getUserPhone());
				}
			}
		}
	}
	
	private void sendSmsFullApi(List<Map<String, Object>> userEmailListAll, SignerBean signer, MsMsgTemplate msgTemplate, MsTenant tenant, MsVendor vendor, TrDocumentH docH, TrDocumentD docD, AuditContext audit) {
		MsLov smsGateway = tenant.getLovSmsGateway();
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signer.getEmail(), false, audit);
		String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
		
		if (null != smsGateway) {
			if (GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS.equals(smsGateway.getCode())) {
				JatisSmsRequestBean jatisReq = new JatisSmsRequestBean(tenant, signer.getUserPhone(), msgTemplate.getBody(), trxNo, false);
				sendSmsJatis(jatisReq, signer.getEmail(), userEmailListAll, docH, docD, user, tenant, audit);
			} else {
				sendSmsVFirst(userEmailListAll, signer, msgTemplate, tenant, vendor, docH, docD, audit);
			}
		} else {
			sendSmsVFirst(userEmailListAll, signer, msgTemplate, tenant, vendor, docH, docD, audit);
		}
	}
	
	
	private void sendSmsJatis(JatisSmsRequestBean jatisReq, String email, List<Map<String, Object>> userEmailListAll, TrDocumentH docH, TrDocumentD docD, AmMsuser user, MsTenant tenant, AuditContext audit) {
		JatisSmsResponse smsResp = jatisSmsLogic.sendSmsOnly(jatisReq);
		LOG.info(email);
		if ("1".equals(smsResp.getStatus())) {
			MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
			MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
			String trxNo = String.valueOf(daoFactory.getCommonDao().nextSequenceTrBalanceMutationTrxNo());
			MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
			String notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, jatisReq.getPhoneNumber());
			MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
			MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_JATIS);
			
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), -1, trxNo, user, notes, smsResp.getMessageId(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg , trxNo, smsResp.getMessageId(),
					jatisReq.getPhoneNumber(), NotificationType.SMS_JATIS, gateway, sendingPoint, audit);

			insertAuditTrailHeader(userEmailListAll, email, true);
		} else {
			insertAuditTrailHeader(userEmailListAll, email, false);
		}
	}
	
	private void sendSmsVFirst(List<Map<String, Object>> userEmailListAll, SignerBean signer, MsMsgTemplate msgTemplate, MsTenant tenant, MsVendor vendor, TrDocumentH docH, TrDocumentD docD, AuditContext audit) {
		MsLov balanceType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_BALANCE_TYPE, GlobalVal.CODE_LOV_BALANCE_TYPE_SMS);
		MsLov trxType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_TRX_TYPE, GlobalVal.CODE_LOV_TRX_TYPE_USMS);
		MsLov sendingPoint = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_NOTIF_SENDING_POINT, GlobalVal.CODE_LOV_NOTIF_SENDING_POINT_SEND_DOCUMENT);
		MsLov gateway = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SMS_GATEWAY, GlobalVal.CODE_LOV_SMS_GATEWAY_VFIRST);
		SendSmsValueFirstRequestBean sendSmsValueFirstRequestBean = new SendSmsValueFirstRequestBean(signer.getUserPhone(), msgTemplate.getBody(), tenant);
		SendSmsResponse smsResponse = smsLogic.sendSms(sendSmsValueFirstRequestBean);
		
		
		/*
		 * Code review Axel 2023-05-04
		 * getUserByLoginId diganti validateGetUserByEmailv2 untuk kasus multi PSrE
		 */
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(signer.getEmail(), false, audit);
		String notes = String.format(RESEND_SIGN_REQ_SMS_NOTES_FORMAT, signer.getUserPhone());
		MsVendor vendorEsg = daoFactory.getVendorDao().getVendorByCode(GlobalVal.VENDOR_CODE_ESG);
		
		List<SignerBean> signerBeans = new ArrayList<>();
		signerBeans.add(signer);
		
		if (smsResponse.getErrorCode() == null || 
				(!smsResponse.getErrorCode().equals("28682") 
				&& !smsResponse.getErrorCode().equals("28681")
				&& !smsResponse.getErrorCode().equals("408"))) {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), -1, String.valueOf(smsResponse.getTrxNo()), user, notes, smsResponse.getGuid(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			messageDeliveryReportLogic.insertMessageDeliveryReport(tenant, vendorEsg, String.valueOf(smsResponse.getTrxNo()), smsResponse.getGuid(),
					signer.getUserPhone(), NotificationType.SMS_VFIRST, gateway, sendingPoint, audit);
			if (StringUtils.isNotBlank(smsResponse.getErrorCode())) {
				inserErrorHistory(docH.getMsBusinessLine().getBusinessLineCode(), null, docH.getMsOffice().getOfficeName(), docH.getRefNumber(),
						signerBeans, signer.getUserName(), notes + ERROR + smsResponse.getErrorCode(), GlobalVal.ERROR_TYPE_ERROR, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);
				insertAuditTrailHeader(userEmailListAll, signer.getEmail(), false);
			} else {
				insertAuditTrailHeader(userEmailListAll, signer.getEmail(), true);
			}
		} else {
			saldoLogic.insertBalanceMutation(null, docH, docD, balanceType, trxType, tenant, vendorEsg, new Date(), docH.getRefNumber(), 0, String.valueOf(smsResponse.getTrxNo()), user, notes + ERROR + smsResponse.getErrorCode(), smsResponse.getGuid(), docH.getMsOffice(), docH.getMsBusinessLine(), audit);
			inserErrorHistory(docH.getMsBusinessLine().getBusinessLineCode(), null, docH.getMsOffice().getOfficeName(), docH.getRefNumber(),
					signerBeans, signer.getUserName(), notes + ERROR + smsResponse.getErrorCode(), GlobalVal.ERROR_TYPE_ERROR, tenant, vendor, GlobalVal.CODE_LOV_ERR_HIST_MODULE_SEND_DOC, audit);
			insertAuditTrailHeader(userEmailListAll, signer.getEmail(), false);
		}
	}
	
	private void sendSignNotifAt(SignerBean signer, String loginId, String documentId, String vendorCode) {
		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(loginId, vendorCode);
		AmMsuser amMsuser = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		
		Map<String, Object> user = new HashMap<>();
		user.put(MAP_KEY_FULLNAME, amMsuser.getFullName());
		user.put(MAP_KEY_EMAIL, registeredUser.getSignerRegisteredEmail());
		user.put("docId", doc.getDocumentId());
		user.put("refNo", doc.getTrDocumentH().getRefNumber());
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", user);
		
		MsMsgTemplate template = msgTemplateLogic.getAndParseContent(GlobalVal.TEMPLATE_NOTIF_AUTOSIGN, templateParameters);
		String[] recipient = {registeredUser.getSignerRegisteredEmail()};
		
		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());
		
		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}
	
	private void sendSignRequestEmail(SignerBean[] signer, String documentId, String[] password, List<Map<String, Object>> userEmailList, List<Map<String, Object>> userEmailListAll, MsTenant tenant, MsVendor vendor) {
		int numOfSigners = userEmailList.size();
		LOG.info("numOfSigners : {}", numOfSigners);
		for (int i = 0; i < numOfSigners; i++) {
			String loginId = signer[i].getLoginId();
			// Check if email has been sent once to a user
			if (!isEmailSent(userEmailListAll, loginId)) {
				boolean isFirstTimeUser = null != password && StringUtils.isNotBlank(password[i]);
				String pass = null != password ? password[i] : null;
				LOG.info("Sending signature request to Email: {}, Password: {}, first time user = {}", loginId, pass, isFirstTimeUser);
				if (isFirstTimeUser && (null != password && StringUtils.isNotBlank(password[i]))) {
					// penjagaan password notBlank karena untuk user existing yg belum aktivasi, password set null
					// asumsi user harus lihat email sebelumnya untuk cek password
					sendSignRequestToNewUser(signer[i], loginId, documentId, password[i],tenant.getTenantName(), false);
				} else {
					if ("at".equals(signer[i].getSignAction())) {
						sendSignNotifAt(signer[i], loginId, documentId, vendor.getVendorCode());
					} else {
						sendSignRequestToUser(signer[i], loginId, documentId,tenant.getTenantName(), vendor.getVendorCode(), false);
					}
				}
				
				insertAuditTrailHeader(userEmailListAll, loginId, true);
			} else if ("at".equals(signer[i].getSignAction())) {
				sendSignNotifAt(signer[i], loginId, documentId, vendor.getVendorCode());
			}
		}
	}
	
	private boolean isEmailSent(List<Map<String, Object>> userEmailList, String email) {
		for (int i = 0; i < userEmailList.size(); i++) {
			String emailFromList = (String) userEmailList.get(i).get(MAP_KEY_EMAIL);
			boolean emailSent = (boolean) userEmailList.get(i).get(MAP_KEY_EMAIL_SENT);
			if (email.equalsIgnoreCase(emailFromList)) {
				if (emailSent) {
					return true;
				} else {
					userEmailList.get(i).put(MAP_KEY_EMAIL_SENT, true);
					return false;
				}
			}
		}
		
		return false;
	}
	
	private void sendSignRequestToNewUser(SignerBean signer, String loginId, String documentId,String password, String tenantName, boolean isManual) {
		
		AmMsuser amMsuser = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		
		Map<String, Object> user = new HashMap<>();
		user.put(MAP_KEY_FULLNAME, amMsuser.getFullName());
		user.put(MAP_KEY_EMAIL, amMsuser.getLoginId());
		user.put(MAP_KEY_PASSWORD, password);
		user.put("link", generateSignLink(documentId));
		String documentName = null != doc.getMsDocTemplate() ? doc.getMsDocTemplate().getDocTemplateName() : doc.getDocumentName();
		user.put("document", documentName);
		user.put("refno", doc.getTrDocumentH().getRefNumber());
		user.put(MAP_KEY_TENANT, tenantName);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", user);
		String templateCode = isManual ? GlobalVal.TEMPLATE_REGISTER_NEWUSER_TTD_MNL : GlobalVal.TEMPLATE_REGISTER_NEWUSER_TTD;
		MsMsgTemplate template = msgTemplateLogic.getAndParseContent(templateCode, templateParameters);
		String[] recipient = {loginId};
		
		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());
		
		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}
	
	private void sendSignRequestToUser(SignerBean signer, String loginId, String documentId,String tenantName, String vendorCode, boolean isManual) {
		
		MsVendorRegisteredUser registeredUser = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserBySignerRegisteredEmailAndVendorCode(loginId, vendorCode);
		AmMsuser amMsuser = daoFactory.getUserDao().getUserByIdNo(signer.getIdNo());
		TrDocumentD doc = daoFactory.getDocumentDao().getDocumentDetailByDocId(documentId);
		
		Map<String, Object> user = new HashMap<>();
		user.put(MAP_KEY_FULLNAME, amMsuser.getFullName());
		user.put(MAP_KEY_EMAIL, registeredUser.getSignerRegisteredEmail());
		user.put("link", generateSignLink(documentId));
		String documentName = null != doc.getMsDocTemplate() ? doc.getMsDocTemplate().getDocTemplateName() : doc.getDocumentName();
		user.put("document", documentName);
		user.put("refno", doc.getTrDocumentH().getRefNumber());
		user.put(MAP_KEY_TENANT, tenantName);
		
		Map<String, Object> templateParameters = new HashMap<>();
		templateParameters.put("user", user);
		
		String templateCode = isManual ? GlobalVal.TEMPLATE_USER_TTD_MANUAL : GlobalVal.TEMPLATE_USER_TTD;
		MsMsgTemplate template = msgTemplateLogic.getAndParseContent(templateCode, templateParameters);
		String[] recipient = {registeredUser.getSignerRegisteredEmail()};
		
		EmailInformationBean emailBean = new EmailInformationBean();
		emailBean.setFrom(fromEmailAddr);
		emailBean.setTo(recipient);
		emailBean.setBodyMessage(template.getBody());
		emailBean.setSubject(template.getSubject());
		
		try {
			emailSenderLogic.sendEmail(emailBean, null);
		} catch (MessagingException e) {
			throw new DocumentException(ReasonDocument.UNKNOWN);
		}
	}
	
	private String generateSignLink(String docId) {
		StringBuilder link = new StringBuilder().append(linkTtdEsign).append(docId);
		return link.toString();
	}

	@Override
	public SendDocFullApiResponse sendDocFullApi(SendDocFullApiRequest request, String apiKey, AuditContext audit) throws Exception {
		SendDocFullApiResponse response = new SendDocFullApiResponse();
		MsTenant tenant = tenantValidatorLogic.validateGetTenant(request.getTenantCode(), true, audit);
		MsVendor vendor = this.checkRequestTemplateAndVendorFullApi(request.getRequests().get(0).getDocumentTemplateCode(), request.getTenantCode(), request.getPsreCode(), audit);
		String[] split = apiKey.split("@");
		Map<String, String> phones = new HashMap<>();
		Map<String, String> emails = new HashMap<>();
		
		if (StringUtils.length(request.getRequests().get(0).getReferenceNo()) > 50) {
			throw new ParameterException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_DOC_INVALID_REF_NUMBER_LENGTH, null, this.retrieveLocaleAudit(audit)), ReasonParam.INVALID_LENGTH);
		}
		
		try {
			checkTenantApiKey(split[1], tenant, audit);
			checkRequestArrayFullApi(request.getRequests(), tenant, vendor, phones, emails, audit);
			this.rejectUnregisteredSignerFullApi(request.getRequests(), vendor, audit);
			for (SendDocFullApiRequestBean docs : request.getRequests()) {
				checkRegisteredUserExternal(docs.getSigners(), tenant, audit);
			}
			this.checkDuplicateRefNumber(request.getRequests().get(0).getReferenceNo(), tenant, audit);
		} catch (Exception e) {
			String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
			insertErrorHistorySendDocFullApi(request.getRequests().get(0), msg, tenant, vendor, audit);
			
			throw e;
		}
		
		MsOffice office = null;
		MsBusinessLine businessLine = null;
		MsRegion region = null;
		AmMsuser user = null;
		
		List<Map<String, Object>> userEmailList = new ArrayList<>();
		
		List<String> emailListed = new ArrayList<>();
		for (SendDocFullApiRequestBean req : request.getRequests()) {
			validateSignerRegistrationExternal(req.getSigners(), vendor, audit);
			for (SendDocFullApiSignerBean signer : req.getSigners()) {
				this.deactivateInvLink(signer.getEmail(), signer.getTlp(), signer.getIdKtp(), vendor.getVendorCode(), audit);
				this.checkRegisteredEmailAndPhone(signer, vendor, audit);
				if (!emailListed.contains(signer.getEmail())) {
					TrSigningProcessAuditTrail auditTrail = setAuditTrailFullApi(signer, tenant, vendor, audit);
					Map<String, Object> email = new HashMap<>();
					email.put(MAP_KEY_EMAIL, signer.getEmail());
					email.put(MAP_KEY_EMAIL_SENT, false);
					email.put(MAP_KEY_PHONE, signer.getTlp());
					email.put(MAP_KEY_SIGN_ACTION, signer.getSignAction());
					email.put(MAP_KEY_AUDIT_TRAIL, auditTrail);
					userEmailList.add(email);
					emailListed.add(signer.getEmail());
				}
				
				if (null == user && signer.getSignerType().equals(GlobalVal.CODE_LOV_SIGNER_TYPE_CUST)) {
					user = daoFactory.getUserDao().getUserByIdNo(signer.getIdKtp());
				}
			}
			
			region = StringUtils.isNotBlank(req.getRegionCode()) ? checkRegionSendDoc(req.getRegionCode(), req.getRegionName(), tenant, audit) : null;
			office = StringUtils.isNotBlank(req.getOfficeCode()) ? checkOfficeSendDoc(req.getOfficeCode(), req.getOfficeName(), tenant, region, audit) : null;
			businessLine = StringUtils.isNotBlank(req.getBusinessLineCode()) ? checkBusinessLineSendDoc(req.getBusinessLineCode(), req.getBusinessLineName(), tenant, audit) : null;
			
			if (null == office) {
				throw new DocumentException(messageSource.getMessage("businesslogic.document.mandatorycannotbeempty", new Object[] {"Office Code"}, this.retrieveLocaleAudit(audit))
						, ReasonDocument.OFFICE_NOT_EXISTS);
			}
		
		}
		
		try {
			TrDocumentH docH = documentLogic.insertDocumentH(request.getRequests().get(0).getReferenceNo(), 
					user, office, tenant, request.getRequests().size(), 0, GlobalVal.DOC_TYPE_AGREEMENT,
					null, null, businessLine, audit);
			
			String prevFile = null;
			List<SendDocFullApiResponseBean> documents = new ArrayList<>();
			for (SendDocFullApiRequestBean docReq : request.getRequests()) {
				// Set manual karena confins tidak mengirim login id dan tidak bisa double serializable
				if (null == prevFile) {
					prevFile = docReq.getDocumentFile();
				} else {
					if (isSameFile(prevFile, docReq.getDocumentFile())) {
						throw new DuplicateRequestException(this.messageSource.getMessage("businesslogic.document.samefile", null, 
								this.retrieveLocaleAudit(audit)));
					}
				}
				
				SendDocFullApiResponseBean document = sendDocumentOneByOneFullApi(tenant, vendor, docReq, docH, userEmailList, audit);
				documents.add(document);
			}	
			
			response.setDocuments(documents);
		} catch (Exception e) {
			String msg = StringUtils.isNotBlank(e.getLocalizedMessage()) ? e.getLocalizedMessage() : e.toString();
			this.insertErrorHistorySendDocFullApi(request.getRequests().get(0), msg, tenant, vendor, audit);
			
			throw e;
		}
		
		return response;
	}

	private void validateSignerRegistrationExternal(List<SendDocFullApiSignerBean> signers, MsVendor vendor, AuditContext audit) {
		for (SendDocFullApiSignerBean signer : signers) {
			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao()
				.getVendorRegisteredUserByIdNoAndVendorCode(signer.getIdKtp(), vendor.getVendorCode());

			if ("at".equalsIgnoreCase(signer.getSignAction())) {
				validateAutosignSignerExternal(signer, vru, audit);
			} else {
				validateManualSignerExternal(signer, vru, audit);
			}
		}
	}

	private void validateAutosignSignerExternal(SendDocFullApiSignerBean signer, MsVendorRegisteredUser vru, AuditContext audit) {
		if (StringUtils.isBlank(vru.getPoaId()) ||
			StringUtils.isBlank(vru.getVendorUserAutosignCvv()) ||
			StringUtils.isBlank(vru.getVendorUserAutosignKey()) ||
			vru.getCertPoaExpiredDate() == null) {
			throw new UserException(messageSource.getMessage("businesslogic.document.poacert.incomplete", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.POA_CERT_INCOMPLETE);
		}
		if (vru.getCertPoaExpiredDate().before(new Date())) {
			throw new UserException(messageSource.getMessage("businesslogic.document.poacert.expired", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.POA_CERT_EXPIRED);
		}
	}

	private void validateManualSignerExternal(SendDocFullApiSignerBean signer, MsVendorRegisteredUser vru, AuditContext audit) {
		if (StringUtils.isBlank(vru.getVendorRegistrationId())) {
			throw new UserException(messageSource.getMessage("businesslogic.document.vendorregistrationidempty", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_REGISTRATION_ID_EMPTY);
		}
		if (!"1".equals(vru.getIsActive())) {
			throw new UserException(messageSource.getMessage("businesslogic.document.notactive", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.VENDOR_USER_NOT_ACTIVE);
		}
		if (vru.getCertExpiredDate() == null) {
			throw new UserException(messageSource.getMessage("businesslogic.document.certdate.notfound", new Object[]{signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonUser.CERT_EXPIRED_DATE_EMPTY);
		}
	}
	
	private void checkRegisteredUserExternal(List<SendDocFullApiSignerBean> signers, MsTenant tenant, AuditContext audit) {
		for (SendDocFullApiSignerBean signer : signers) {
			AmMsuser user = daoFactory.getUserDao().getUserByIdNo(signer.getIdKtp());
			AmMemberofrole mor = daoFactory.getRoleDao().getMemberofroleByLoginIdRoleTenantCode(user.getLoginId(), tenant.getTenantCode());
			if (null == mor) {
				this.insertRoleNewUser(user, signer.getSignerType(), tenant, audit);
			}
			
			MsUseroftenant uot = daoFactory.getTenantDao().getUseroftenantByLoginIdTenantCode(user.getLoginId(), tenant.getTenantCode());
			if (null == uot ) {
				this.insertUseroftenant(user, tenant, audit);
			}
		}
	}
	
	private void checkTenantApiKey(String tenantApi, MsTenant tenant, AuditContext audit) {
		if (!tenant.getTenantCode().equalsIgnoreCase(tenantApi)) {
			throw new TenantException(messageSource.getMessage("businesslogic.tenant.apikeyinvalidforagreement", null, this.retrieveLocaleAudit(audit)), ReasonTenant.VALIDATE_TENANT_API_KEY_ERROR);
		}
	}
	
	private void checkRequestArrayFullApi(List<SendDocFullApiRequestBean> requests, MsTenant tenant, MsVendor vendor,
			Map<String, String> phones, Map<String, String> emails, AuditContext audit) {
		
		if (null == requests || requests.isEmpty()) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
					new Object[] {"SendDocFullApiRequestBean"}, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		String refNo = "";
		for (SendDocFullApiRequestBean request : requests) {
			 List<String>  signerTypes = this.checkRequestFullApi(request, refNo, tenant, audit);
			
			 List<String> availSigner = new ArrayList<>();
			//check data signer 
			for (SendDocFullApiSignerBean signer : request.getSigners()) {
				this.checkRequestSignerFullApi(signer, vendor, phones, emails, audit);
				availSigner.add(signer.getSignerType());
			}
			
			if (!signerTypes.isEmpty()) {
				for(String signerType : signerTypes) {
					if (!availSigner.contains(signerType)) {
						throw new DocumentException(messageSource.getMessage("businesslogic.document.mustsendsigner", 
								new Object[] {signerType, request.getDocumentTemplateCode()},  this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
					}
				}
			}
		}
	}
	
	private void checkRegisteredEmailAndPhone(SendDocFullApiSignerBean signer, MsVendor vendor, AuditContext audit) {
		MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(signer.getIdKtp(), vendor.getVendorCode());
		String phoneVru = personalDataEncLogic.decryptToString(vru.getPhoneBytea());
		if (!signer.getTlp().equals(phoneVru)) {
			signer.setTlp(phoneVru);
		}
		
		if (!signer.getEmail().equalsIgnoreCase(vru.getSignerRegisteredEmail())) {
			signer.setEmail(vru.getSignerRegisteredEmail());
		}
		
		signer.setIsActive(vru.getIsActive());
	}
	
	private List<String> checkRequestFullApi(SendDocFullApiRequestBean request, String refNo, MsTenant tenant, AuditContext audit) {
		List<String> signers  = new ArrayList<>();
		String messageValidation = "";
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] { "referenceNo" }, audit);
		commonValidatorLogic.validateNotNull(request.getReferenceNo(), messageValidation, StatusCode.REFERENCE_NO_NOT_EXISTS);
	
		// Ref No antar dokumen harus sama
		if (refNo.equals("")) {
			refNo = request.getReferenceNo();
			LOG.info("Send Document with Ref Number : {}", refNo);
		} else if (!refNo.equals(request.getReferenceNo())) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.mismatchedrefno", null, this.retrieveLocaleAudit(audit)), ReasonDocument.DIFFERENT_REFF_NO);
		}
		
		// Doc Template Code invalid
		if (StringUtils.isNoneBlank(request.getDocumentTemplateCode())) {
			MsDocTemplate dt = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(request.getDocumentTemplateCode(), tenant.getTenantCode());
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
					new Object[] { request.getDocumentTemplateCode() }, audit);
			commonValidatorLogic.validateNotNull(dt, messageValidation, StatusCode.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
		
		
			signers = this.getDocTemplateSignerTypes(request.getDocumentTemplateCode(), tenant.getIdMsTenant());
		}
		
		//Doc Template empty and signloc not sent
		if (StringUtils.isBlank(request.getDocumentTemplateCode())) {
			
			if (!isSignLocSent(request)) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
						new Object[] {GlobalVal.CONST_DOCUMENT_TEMPLATE_CODE}, this.retrieveLocaleAudit(audit)), 
							ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
			}
			
			//sign loc incomplete
			if (!isSignLocComplete(request)) {
				throw new DocumentException(this.messageSource.getMessage("businesslogic.document.stampsignincomplete", null
						, this.retrieveLocaleAudit(audit)), 
							ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
			}
			
			messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
					new Object[] { "documentName" }, audit);
			commonValidatorLogic.validateNotNull(request.getDocumentName(), messageValidation, StatusCode.DOCUMENT_FILE_NOT_EXISTS);
		
			
		}
		messageValidation = getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] { "documentFile" }, audit);
		commonValidatorLogic.validateNotNull(request.getDocumentFile(), messageValidation, StatusCode.DOCUMENT_FILE_NOT_EXISTS);
	
		
		if (request.getDocumentFile().length() % 4 != 0) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.docfileinvalid", null, this.retrieveLocaleAudit(audit)), 
					ReasonDocument.DOC_FILE_INVALID);
		}
		
		try {
			Base64.getDecoder().decode(request.getDocumentFile());
		} catch (Exception e) {
			throw new DocumentException(messageSource.getMessage("businesslogic.document.docfileinvalid", null, this.retrieveLocaleAudit(audit)), 
					ReasonDocument.DOC_FILE_INVALID);
		}
		
		// Signer kosong
		if (null == request.getSigners() || request.getSigners().isEmpty()) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {"signer"}, this.retrieveLocaleAudit(audit)), ReasonDocument.UNKNOWN);
		}
		
		return signers;
	}
	
	private List<String> getDocTemplateSignerTypes(String docTemplateCode, long idTenant) {
		List<String> signers = new ArrayList<>();
		List<MsDocTemplateSignLoc> sls = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeAndIdTenant(docTemplateCode, idTenant);
		for (MsDocTemplateSignLoc sl : sls) {
			MsLov signerType = sl.getMsLovByLovSignerType();
			if (null != signerType && !signers.contains(signerType.getCode())) {
				signers.add(signerType.getCode());
			}
		}
		
		return signers;
	}
	
	private void checkRequestSignerFullApi(SendDocFullApiSignerBean signer, MsVendor vendor, 
			Map<String, String> phones, Map<String, String> emails, AuditContext audit) {
		if (StringUtils.isBlank(signer.getTlp())) {
			throw new DocumentException(messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST, new Object[] {"tlp"}, this.retrieveLocaleAudit(audit)), ReasonDocument.ERROR_EXIST);
		}
		
		if (StringUtils.isBlank(signer.getSignAction())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {MAP_KEY_SIGN_ACTION}, this.retrieveLocaleAudit(audit)), ReasonDocument.SIGN_ACTION_NOT_EXISTS);
		} 
		
		List<String> signActions = new ArrayList<>();
		signActions.add("at"); signActions.add("mt");
		if (!signActions.contains(signer.getSignAction())) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.invalidsignaction",
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		
		if (StringUtils.isBlank(signer.getSignerType())) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
				new Object[] {"signerType"}, this.retrieveLocaleAudit(audit)), ReasonDocument.SIGNER_TYPE_NOT_EXISTS);
		}
		
		List<String> signerTypes = daoFactory.getLovDao().getListofCodeByLovGroup(GlobalVal.LOV_GROUP_SIGNER_TYPE);
		if (!signerTypes.contains(StringUtils.upperCase(signer.getSignerType()))) {
			throw new DocumentException(this.messageSource.getMessage("businesslogic.document.invalidsignertype",
					null, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		
		
		
		if (StringUtils.equals("-", StringUtils.trim(signer.getEmail()))) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_EMAIL,
					new Object[] {signer.getEmail()}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		
		if (StringUtils.equals("-", StringUtils.trim(signer.getTlp()))) {
			throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_INV_LINK_INVALID_PHONE,
					new Object[] {signer.getTlp()}, this.retrieveLocaleAudit(audit)), ReasonDocument.PARAM_INVALID);
		}
		
		this.validateSendDocumentNikExternal(signer.getIdKtp(), signer.getEmail(), vendor.getVendorCode(), audit);
		this.checkEmailAndPhoneInRequest(phones, emails, signer.getTlp(), signer.getEmail(), signer.getIdKtp(), audit);
		
		if(!(signer.getSignerType().equals("MF") && signer.getSignAction().equals("at"))) {
			this.checkIdNoPhoneAndEmail(signer.getIdKtp(), signer.getTlp(), signer.getEmail(), vendor.getVendorCode(), audit);
		}
	}
	
	private void rejectUnregisteredSignerFullApi(List<SendDocFullApiRequestBean> requests, MsVendor vendor, AuditContext audit) {
		for (SendDocFullApiRequestBean request : requests) {
			for (SendDocFullApiSignerBean signer : request.getSigners()) {
				MsLov signerType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNER_TYPE, signer.getSignerType());
				if(!signer.getSignerType().contains("MF") && signer.getSignAction().equals("at")) {
					throw new UserException(messageSource.getMessage("businesslogic.user.signernotallowed",
							new String[] {signer.getSignerType(), signer.getTlp()}, retrieveLocaleAudit(audit)), ReasonUser.PARAM_INVALID);
				}
				rejectUnregisteredNik(signer.getIdKtp(), vendor.getVendorCode(), signerType.getDescription(), audit);
				if(!signer.getSignerType().contains("MF") || !signer.getSignAction().equals("at")) {
					rejectUnregisteredPhone(signer.getTlp(), vendor.getVendorCode(), signerType.getDescription(), audit, signer.getIdKtp());
				}
				rejectUnregisteredEmail(signer.getEmail(), vendor.getVendorCode(), signerType.getDescription(), audit);
			}
		}
	}
	
	private boolean isSignLocSent(SendDocFullApiRequestBean request) {
		for (SendDocFullApiSignerBean signer : request.getSigners()) {
			if (null == signer.getSignLocations() || signer.getSignLocations().isEmpty()) {
				return false;
			}
		}
		return true;
	}
	
	private boolean isSignLocComplete(SendDocFullApiRequestBean request) {
		for (SendDocFullApiSignerBean signer : request.getSigners()) {
			for (SignLocationFullApiBean sl : signer.getSignLocations()) {
				if (null == sl.getLlx() || null == sl.getLly() || null == sl.getUrx() || null == sl.getUry() || 0 == sl.getPage()) {
					return false;
				}
			}
			
			if (null != request.getStampLocations()) {
				for (SignLocationFullApiBean sl : request.getStampLocations()) {
					if (null == sl.getLlx() || null == sl.getLly() || null == sl.getUrx() || null == sl.getUry() || 0 == sl.getPage()) {
						return false;
					}
				}
			}
		}
		
		return true;
	}

	private void checkRequestTemplateAndVendorV2(DocumentConfinsRequestBean[] reqBeans, String tenantCode, AuditContext audit) {
		for (DocumentConfinsRequestBean bean : reqBeans) {
			if (StringUtils.isBlank(bean.getDocumentTemplateCode())) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_LOGIC_MANDATORYPARAM_INEXIST,
					new Object[] {GlobalVal.CONST_DOCUMENT_TEMPLATE_CODE}, this.retrieveLocaleAudit(audit)), 
						ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
			}
			
			MsDocTemplate docTemplate = daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(
					bean.getDocumentTemplateCode(), tenantCode);
			
			if (null == docTemplate) {
				throw new DocumentException(this.messageSource.getMessage(GlobalKey.MESSAGE_ERROR_GLOBAL_ENTITY_NOT_FOUND,
						new Object[] {bean.getDocumentTemplateCode()}, this.retrieveLocaleAudit(audit)), 
							ReasonDocument.DOCUMENT_TEMPLATE_CODE_NOT_EXISTS);
			}
			
			MsVendor vendor = docTemplate.getMsVendor();
			
			if (null == vendor || !vendor.getIsOperating().equals("1")) {
				vendor = vendorValidatorLogic.validateGetOperatingDefaultVendor(tenantCode, false, audit);
			}
		}
	}
	
	private void checkRequestArrayV2(DocumentConfinsRequestBean[] reqBeans, String tenantCode, String vendorCode, AuditContext audit) {
		Map<String, String> phones = new HashMap<>();
		Map<String, String> emails = new HashMap<>();
		
		for (DocumentConfinsRequestBean bean : reqBeans) {
			this.checkRequest(bean, tenantCode, vendorCode, phones, emails, audit);
		}
	}

    private void validateNotificationDailyAttempt(TrInvitationLink invLink, MsTenant tenant, AuditContext audit) {
        int dailyLimit = tenantSettingsLogic.getSettingValue(tenant, "MAX_NOTIF_SENDING_POINT_DAILY", 0);
        if (dailyLimit == 0) {
            String gsValue = daoFactory.getGeneralSettingDao().getGsValueByCode("MAX_NOTIF_SENDING_POINT_DAILY");
            try {
                dailyLimit = Integer.parseInt(gsValue);
            } catch (Exception e) {
                dailyLimit = 0;
            }
        }
        if (dailyLimit == 0) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitnotset", null, audit), ReasonSendNotif.NOTIF_LIMIT_NOT_SET);
        }

        if (invLink.getNotificationAttemptDate() == null || invLink.getNotificationAttemptNum() == null || !DateUtils.isSameDay(invLink.getNotificationAttemptDate(), new Date())) {
            invLink.setNotificationAttemptNum((short) 0);
        }

		if (invLink.getNotificationAttemptNum() >= dailyLimit) {
            throw new SendNotificationException(getMessage("businesslogic.notification.dailylimitreached", null, audit), ReasonSendNotif.NOTIF_DAILY_LIMIT_REACHED);
        }

        invLink.setNotificationAttemptNum((short) (invLink.getNotificationAttemptNum() + 1));
        invLink.setNotificationAttemptDate(new Date());

        invLink.setUsrUpd(audit.getCallerId());
        invLink.setDtmUpd(new Date());
        daoFactory.getInvitationLinkDao().updateInvitationLink(invLink);
    }
}

