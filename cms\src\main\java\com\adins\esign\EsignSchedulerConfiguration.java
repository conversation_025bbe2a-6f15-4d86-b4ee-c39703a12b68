package com.adins.esign;

import java.text.ParseException;
import java.util.Calendar;

import org.quartz.CronTrigger;
import org.quartz.JobDetail;
import org.quartz.SchedulerException;
import org.quartz.SimpleTrigger;
import org.quartz.Trigger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.scheduling.quartz.SimpleTriggerFactoryBean;

@Configuration
public class EsignSchedulerConfiguration implements SchedulerFactoryBeanCustomizer {
	
	private static final Logger LOG = LoggerFactory.getLogger(EsignSchedulerConfiguration.class);
	private static final String CONST_EMAIL_REMINDER_JOB = "emailReminderJob";
	private static final String CONST_TAKE_QUEUE_SAVE_SIGNINGRESULT = "takeQueueSaveSigningResult";
	
	@Autowired
	private ApplicationContext applicationContext;
	@Autowired
	private Environment environment;
	
	@Override
	public void customize(SchedulerFactoryBean schedulerFactoryBean) {
		schedulerFactoryBean.setAutoStartup(true );
		schedulerFactoryBean.setApplicationContext(applicationContext);
		schedulerFactoryBean.setExposeSchedulerInRepository(true);
		schedulerFactoryBean.setOverwriteExistingJobs(true);
		schedulerFactoryBean.setWaitForJobsToCompleteOnShutdown(true);
	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.process-automatic-stamp.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerProcessAutomaticStamp() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("processAutomaticStampJob", "runProcessAutomaticStamp", false, "processAutomaticStampJob");
// 		String cron = environment.getProperty("job.process-automatic-stamp.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerProcessAutomaticStamp");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.stamping-on-prem.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerStampingOnPrem() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("stampingOnPremJob", "runStampingOnPrem", false, "stampingOnPremJob");
// 		String cron = environment.getProperty("job.stamping-on-prem.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerStampingOnPrem");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.on-prem-payment-receipt.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerStampingOnPremPaymentReceipt() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("stampingOnPremPaymentReceiptJob", "runStampingOnPremPaymentReceipt", false, "stampingOnPremPaymentReceiptJob");
// 		String cron = environment.getProperty("job.on-prem-payment-receipt.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerStampingOnPremPaymentReceipt");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value = "job.delete-unused-complementary-stamping-document.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerDeleteUnusedComplementaryStampingDocumentJob() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("deleteUnusedComplementaryStampingDocumentJob", "runDeleteDocument", false, "deleteUnusedComplementaryStampingDocumentJob");
// 		String cron = environment.getProperty("job.delete-unused-complementary-stamping-document.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerDeleteUnusedComplementaryStampingDocumentJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value = "job.deleteresultstamping-onprem.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerDeleteDocumentResultOnPremJob() throws SchedulerException {
// 	    JobDetail jobDetail = createJobDetail("deleteOnPremDocResultJob", "runDeleteStampResultDocOnPremJob", false, "deleteOnPremResultDocJob");
// 	    String cron = environment.getProperty("job.deleteresultstamping-onprem.cron");
// 	    return createCronTrigger(jobDetail, cron, "triggerDeleteDocumentResultOnPremJob");
// 	}
	
// //	@Bean
// //	  @ConditionalOnProperty(value = "job.document-type.enabled", matchIfMissing = false, havingValue = "true")
// //	  public Trigger triggerDocumentTypeJob() throws SchedulerException {
// //	      JobDetail jobDetail = createJobDetail("documentTypeJob", "runDocumentType", false, "documentTypeJob");
// //	      String cron = environment.getProperty("job.document-type.cron");
// //	      return createCronTrigger(jobDetail, cron, "triggerDocumentTypeJob");
// //	  }
	
// 	@Bean
//     @ConditionalOnProperty(value = "job.location.enabled", matchIfMissing = false, havingValue = "true")
//     public Trigger triggerLocationJob() throws SchedulerException {
//         JobDetail jobDetail = createJobDetail("locationJob", "runLocation", false, "locationJob");
//         String cron = environment.getProperty("job.location.cron");
//         return createCronTrigger(jobDetail, cron, "triggerLocationJob");
//     }
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.daily-recap.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerDailyRecapJob() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail("dailyRecapJob", "runDailyRecap", false, "dailyRecapJob");
// 		String cron = environment.getProperty("job.daily-recap.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerDailyRecapJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.email-reminder.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerEmailReminderJob() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail(CONST_EMAIL_REMINDER_JOB, "runEmailReminder", false, CONST_EMAIL_REMINDER_JOB);
// 		String cron = environment.getProperty("job.email-reminder.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerEmailReminderJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.queue-balancecheck.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerTakeQueueBalanceCheckJob() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail(CONST_EMAIL_REMINDER_JOB, "takeQueueBalanceCheck", false, "takeQueueBalanceCheckJob");
// 		String cron = environment.getProperty("job.queue-balancecheck.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerTakeQueueBalanceCheckJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.read-email.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerReadEmailJob() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("readEmailJob", "runReadEmail", false, "readEmailJob");
// 		String cron = environment.getProperty("job.read-email.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerReadEmailJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value = "job.delete-email.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerDeleteEmailJob() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("deleteEmailJob", "runDeleteEmail", false, "deleteEmailJob");
// 		String cron = environment.getProperty("job.delete-email.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerDeleteEmailJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.queue-savesignresult.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerTakeQueueSaveSigningResult() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail("signingJob", CONST_TAKE_QUEUE_SAVE_SIGNINGRESULT, false, "takeQueueSaveSigningResultJob");
// 		String cron = environment.getProperty("job.queue-savesignresult.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerTakeQueueSaveSigningResult");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.digi-balancesync.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerSyncDigiBalance() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail("syncDigiBalanceJob", "syncDigiBalance", false, "syncDigiBalanceJob");
// 		String cron = environment.getProperty("job.digi-balancesync.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerSyncDigiBalance");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.digi-certexpsync.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerSyncDigiCertExpDate() throws SchedulerException {		
// 		JobDetail jobDetail = createJobDetail("syncDigiCertExpJob", "syncDigiCertExp", false, "syncDigiCertExpJob");
// 		String cron = environment.getProperty("job.digi-certexpsync.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerSyncDigiCertExpDate");
// 	}
	
// //	@Bean
// //	@ConditionalOnProperty(value="job.mcp-attachmeterai.enabled", matchIfMissing=false, havingValue="true")
// //	public Trigger triggerAttachMeterai() throws SchedulerException {
// //		JobDetail jobDetail = createJobDetail("attachMeteraiJob", "runAttachMeterai", false, "runAttachMeteraiJob");
// //		String cron = environment.getProperty("job.mcp-attachmeterai.cron");
// //		return createCronTrigger(jobDetail, cron, "triggerRunAttachMeteraiJob");
// //	}
// //	
// //	@Bean
// //	@ConditionalOnProperty(value="job.rkg-attachmeterai.enabled", matchIfMissing=false, havingValue="true")
// //	public Trigger triggerAttachMeteraiRkg() throws SchedulerException {
// //		JobDetail jobDetail = createJobDetail("attachMeteraiJob", "runAttachMeteraiRkg", false, "runAttachMeteraiRkgJob");
// //		String cron = environment.getProperty("job.rkg-attachmeterai.cron");
// //		return createCronTrigger(jobDetail, cron, "triggerRunAttachMeteraiRkgJob");
// //	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.pajakku-attachmeterai.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerAttachMeteraiPajakku() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("attachMeteraiJob", "runAttachMeteraiPajakku", false, "runAttachMeteraiPajakkuJob");
// 		String cron = environment.getProperty("job.pajakku-attachmeterai.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerRunAttachMeteraiPajakkuJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.stamping-payment-receipt.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerStampingPaymentReceipt() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("stampingPaymentReceiptJob", "runStampingPaymentReceipt", false, "stampingPaymentReceiptJob");
// 		String cron = environment.getProperty("job.stamping-payment-receipt.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerStampingPaymentReceipt");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.sync-payment-receipt.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerSyncConfinsAndUploadDms() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("paymentReceiptConfinsSyncAndDmsUploadJob", "runPaymentReceiptSyncAndDmsUpload", false, "runPaymentReceiptSyncAndDmsUpload");
// 		String cron = environment.getProperty("job.sync-payment-receipt.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerRunPaymentReceiptSyncAndDmsUpload");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.resumeworkflow.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerResumeWorkflow() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("resumeWorkflowJob", "runResumeWorkflow", false, "runResumeWorkflowJob");
// 		String cron = environment.getProperty("job.resumeworkflow.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerRunResumeWorkflowJob");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.uploaddmswf.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerUploadDmsWf() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("uploadDmsWfJob", "runUploadDmsWf", false, "runUploadDmsWfJob");
// 		String cron = environment.getProperty("job.uploaddmswf.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerRunUploadDmsWfJobJob");
// 	}
	
// //	@Bean
// //	@ConditionalOnProperty(value="job.uploadtodms.enabled", matchIfMissing=false, havingValue="true")
// //	public Trigger triggerUploadToDms() throws SchedulerException {
// //		JobDetail jobDetail = createJobDetail("uploadToDmsJob", "runUploadToDms", false, "runUploadToDmsJob");
// //		String cron = environment.getProperty("job.uploadtodms.cron");
// //		return createCronTrigger(jobDetail, cron, "triggerRunUploadToDmsJob");
// //	}
	
// 	@Bean
// 	@ConditionalOnProperty(value = "job.queue-upderrhistrerun.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerUpdErrHistRerunProcess() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("updErrHistRerunProcessJob", "takeQueueUpdErrHistRerunProcess", false, "takeQueueUpdErrHistRerunProcess");
// 		String cron = environment.getProperty("job.queue-upderrhistrerun.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerUpdErrHistRerunProcess");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value = "job.errhist-rerunsenddoc.enabled", matchIfMissing = false, havingValue = "true")
// 	public Trigger triggerErrorHistoryRerunSendDoc() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("errorHistoryRerunSendDocJob", "runErrorHistoryRerunSendDoc", false, "runErrorHistoryRerunSendDoc");
// 		String cron = environment.getProperty("job.errhist-rerunsenddoc.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerErrorHistoryRerunSendDoc");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.queue-savesignresulttekenaja.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerTakeQueueSaveSigningResultTknAja() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("signingTekenAjaJob", CONST_TAKE_QUEUE_SAVE_SIGNINGRESULT, false, "takeQueueSaveSigningResultTknAjaJob");
// 		String cron = environment.getProperty("job.queue-savesignresulttekenaja.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerTakeQueueSaveSigningResultTknAja");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.queue-deletefromoss.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerDeleteFromOss() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("deleteFileFromOssJob", CONST_TAKE_QUEUE_SAVE_SIGNINGRESULT, false, "takeQueueDeleteJob");
// 		String cron = environment.getProperty("job.queue-deletefromoss.enabled.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerDeleteFromOss");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.sign-vida.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerSigningVida() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("signingVidaJob", "runSigningVida", false, "signingVidaJob");
// 		String cron = environment.getProperty("job.sign-vida.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerSigningVida");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.sign-privy.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerSigningPrivy() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("signingPrivyJob", "runSigningPrivy", false, "signingPrivyJob");
// 		String cron = environment.getProperty("job.sign-privy.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerSigningPrivy");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.checkregisstat.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerCheckRegisStat() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("registerPrivyJob", "runCheckRegisterStat", false, "registerPrivyJob");
// 		String cron = environment.getProperty("job.checkregisstat.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerCheckRegisStat");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.attachmeterai-privy.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerAttachMeteraiPrivy() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("attachMeteraiPrivyJob", "runAttachMeteraiPrivy", false, "attachMeteraiPrivyJob");
// 		String cron = environment.getProperty("job.attachmeterai-privy.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerAttachMeteraiPrivy");
// 	}
	
// 	@Bean
// 	@ConditionalOnProperty(value="job.attachmeterai-vida.enabled", matchIfMissing=false, havingValue="true")
// 	public Trigger triggerAttachMeteraiVida() throws SchedulerException {
// 		JobDetail jobDetail = createJobDetail("attachMeteraiVidaJob", "runAttachMeteraiVida", false, "attachMeteraiVidaJob");
// 		String cron = environment.getProperty("job.attachmeterai-vida.cron");
// 		return createCronTrigger(jobDetail, cron, "triggerAttachMeteraiVida");
// 	}
	
	SimpleTrigger createTrigger(JobDetail jobDetail, long pollFrequencyMs, String triggerName) {
        LOG.info("createTrigger(jobDetail={}, pollFrequencyMs={}, triggerName={})", jobDetail, pollFrequencyMs, triggerName);
        SimpleTriggerFactoryBean factoryBean = new SimpleTriggerFactoryBean();
        factoryBean.setJobDetail(jobDetail);
        factoryBean.setStartDelay(0L);
        factoryBean.setRepeatInterval(pollFrequencyMs);
        factoryBean.setName(triggerName);
        factoryBean.setRepeatCount(SimpleTrigger.REPEAT_INDEFINITELY);
        factoryBean.setMisfireInstruction(SimpleTrigger.MISFIRE_INSTRUCTION_RESCHEDULE_NEXT_WITH_REMAINING_COUNT);
        factoryBean.afterPropertiesSet();
        return factoryBean.getObject();
    }
    
	CronTrigger createCronTrigger(JobDetail jobDetail, String cronExpression, String triggerName) throws SchedulerException {
        LOG.info("createCronTrigger(jobDetail={}, cronExpression={}, triggerName={})", jobDetail, cronExpression, triggerName);
        // To fix an issue with time-based cron jobs
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        CronTriggerFactoryBean factoryBean = new CronTriggerFactoryBean();
        factoryBean.setJobDetail(jobDetail);
        factoryBean.setCronExpression(cronExpression);
        factoryBean.setStartTime(calendar.getTime());
        factoryBean.setStartDelay(0L);
        factoryBean.setName(triggerName);
        factoryBean.setMisfireInstruction(CronTrigger.MISFIRE_INSTRUCTION_DO_NOTHING);
        try {
			factoryBean.afterPropertiesSet();
		}
        catch (ParseException e) {
			LOG.error("Error upon creating cron trigger with exp '{}'", cronExpression);
			throw new SchedulerException("Failed upon initialized job scheduler");
        	
		}
        return factoryBean.getObject();
    }
	
	JobDetail createJobDetail(String jobBeanName, String jobMethodName, boolean concurrent, String jobName) throws SchedulerException {
		MethodInvokingJobDetailFactoryBean mijfb = new MethodInvokingJobDetailFactoryBean();
		mijfb.setBeanFactory(applicationContext);
		mijfb.setTargetBeanName(jobBeanName);
		mijfb.setTargetMethod(jobMethodName);
		mijfb.setConcurrent(concurrent);
		mijfb.setName(jobName);
		try {
			mijfb.afterPropertiesSet();
		}
		catch (ClassNotFoundException | NoSuchMethodException e) {
			LOG.error("Error upon creating job {} with method {}", jobBeanName, jobMethodName);
			throw new SchedulerException("Failed upon initialized job scheduler");
		}
		return mijfb.getObject();
	}
}
