@echo off
REM #set url=https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent
REM FOR /f "delims=" %%R in ('svn info --show-item=revision %url%') do @set svnRevision=%%R
REM echo SVN revision: %svnRevision%
REM echo com.adins.esign.cms-4.16.0-r%svnRevision%
java -Xms256m -Xmx512m -Dlogging.config=/eSign/logback-spring.xml -Dserver.port=7021 -Djasypt.encryptor.password=2DTzxKRsey0fSodY -Dhttps.proxyHost=proxy.ad-ins.com -Dhttps.proxyPort=8080 -Dhttp.proxyHost=proxy.ad-ins.com -Dhttp.proxyPort=8080 -Dhttp.proxyUser=susilo.up -Dhttp.proxyPassword=AdIns2014 -Dhttps.proxyUser=susilo.up -Dhttps.proxyPassword=AdIns2014 -Dhttp.nonProxyHosts=b2b-api-av.privy.id^|stg-core.privypass.id^|api.halosis.id^|api.jatismobile.com^|apix.sandbox-111094.com^|m2m.coster.id^|api-b2b.dcistg.id^|vida-sign.s3.ap-southeast-3.amazonaws.com^|vida-cloudstore.s3.ap-southeast-1.amazonaws.com^|services-sandbox.vida.id^|qa-sso.vida.id^|liveness-go3voyqswq-et.a.run.app^|billingserver^|*************^|apix.sandbox-111094.com^|kfx-svr^|api.tandatanganku.com^|apiuat.tandatanganku.com^|andyresearch.my.id^|storm20^|api.wassenger.com^|VMDEV03^|*************^|**************^|docsol.id^|api.cloud.nodeflux.io^|liveness-uhomlq3txa-et.a.run.app^|api.myvaluefirst.com^|api.myvfirst.com^|backendservicestg.e-meterai.co.id^|stampservicestg.e-meterai.co.id^|fileuploadstg.e-meterai.co.id^|stampv2stg.e-meterai.co.id^|smtp.office365.com^|esignhub.my.id^|mail.esignhub.my.id^|web-womf  -Dhttps.nonProxyHosts=b2b-api-av.privy.id^|stg-core.privypass.id^|api.halosis.id^|api.jatismobile.com^|m2m.coster.id^|b2b.dcistg.id^|vida-sign.s3.ap-southeast-3.amazonaws.com^|vida-cloudstore.s3.ap-southeast-1.amazonaws.com^|services-sandbox.vida.id^|qa-sso.vida.id^|liveness-go3voyqswq-et.a.run.app^|billingserver^|*************^|apix.sandbox-111094.com^|kfx-svr^|api.tandatanganku.com^|apiuat.tandatanganku.com^|andyresearch.my.id^|storm20^|api.wassenger.com^|VMDEV03^|*************^|**************^|docsol.id^|api.cloud.nodeflux.io^|liveness-uhomlq3txa-et.a.run.app^|api.myvaluefirst.com^|api.myvfirst.com^|backendservicestg.e-meterai.co.id^|stampservicestg.e-meterai.co.id^|fileuploadstg.e-meterai.co.id^|stampv2stg.e-meterai.co.id^|smtp.office365.com^|esignhub.my.id^|mail.esignhub.my.id^|web-womf -Dserver.servlet.context-path=/adimobile/esign -Dspring.flyway.enabled=false -jar com.adins.esign.cms-4.16.0.war