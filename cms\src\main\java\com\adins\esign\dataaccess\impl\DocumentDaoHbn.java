package com.adins.esign.dataaccess.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.am.businesslogic.BaseDaoHbn;
import com.adins.am.model.AmMsuser;
import com.adins.am.model.custom.ActiveAndUpdateableEntity;
import com.adins.constants.AmGlobalKey;
import com.adins.esign.constants.GlobalKey;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.dataaccess.api.DocumentDao;
import com.adins.esign.dataaccess.factory.api.DaoFactory;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsOffice;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrDocumentDRestore;
import com.adins.esign.model.TrDocumentDSign;
import com.adins.esign.model.TrDocumentDStampduty;
import com.adins.esign.model.TrDocumentH;
import com.adins.esign.model.TrDocumentHStampdutyError;
import com.adins.esign.model.TrDocumentSigningRequest;
import com.adins.esign.model.TrManualReport;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.custom.ActivationDocumentBean;
import com.adins.esign.model.custom.CheckDocumentBeforeSigningBean;
import com.adins.esign.model.custom.DeleteOnPremResultBean;
import com.adins.esign.model.custom.DocHBean;
import com.adins.esign.model.custom.DocumentTemplateBean;
import com.adins.esign.model.custom.DocumentTemplateByTenantBean;
import com.adins.esign.model.custom.DocumentTemplateSignLocationBean;
import com.adins.esign.model.custom.ListReportBean;
import com.adins.esign.model.custom.SignerInfoBean;
import com.adins.esign.model.custom.TaxReportBean;
import com.adins.esign.util.MssTool;
import com.adins.esign.validatorlogic.api.UserValidatorLogic;
import com.adins.esign.webservices.model.ActivationStatusByDocumentId;
import com.adins.esign.webservices.model.GetListReportRequest;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.util.PagingStartEndBean;

@Transactional
@Component
@SuppressWarnings("unchecked")
public class DocumentDaoHbn extends BaseDaoHbn implements DocumentDao {
	
	private AuditInfo auditInfoDT;
	private AuditInfo auditInfoSL;
	private static String start = "start";
	private static String end = "end";
	
	@Autowired DaoFactory daofactory;
	@Autowired UserValidatorLogic userValidatorLogic;
	
	public DocumentDaoHbn() {
		String[] pkColsDT = {MsDocTemplate.ID_DOC_TEMPLATE_HBM};
		String[] pkDbColsDT = {MsDocTemplate.ID_DOC_TEMPLATE_DB};
		String[] colsDT = {MsDocTemplate.ID_DOC_TEMPLATE_HBM, "msLov", "msTenant", MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, "docTemplateName", "docTemplateDescription", "numberOfPage", "docExample", "isSequence", "isActive", "usrCrt", "dtmCrt"};
		String[] dbColsDT = {MsDocTemplate.ID_DOC_TEMPLATE_DB, "lov_payment_sign_type", "id_ms_tenant", "doc_template_code", "doc_template_name", "doc_template_description","number_of_page", "doc_example", "is_sequence", "is_active", "usr_crt", "dtm_crt"};
		this.auditInfoDT = new AuditInfo("ms_doc_template", pkColsDT, pkDbColsDT, colsDT, dbColsDT);
		
		String[] pkColsSL = {"idMsDocTemplateSignLoc"};
		String[] pkDbColsSL = {"id_ms_doc_template_sign_loc"};
		String[] colsSL = {"idMsDocTemplateSignLoc", "msDocTemplate", "msLovByLovSignType", "msLovByLovSignerType", "signLocation", "signPage", "seqNo", "usrCrt", "dtmCrt"};
		String[] dbColsSL = {"id_ms_doc_template_sign_loc", "id_doc_template", "lov_sign_type", "lov_signer_type", "sign_location","sign_page", "seq_no", "usr_crt", "dtm_crt"};
		this.auditInfoSL = new AuditInfo("ms_doc_template_sign_loc", pkColsSL, pkDbColsSL, colsSL, dbColsSL);
	}
	
	private StringBuilder constructParamSearchListDocumentTemplate(Map<String, Object> params, String searchDocTempCode, String searchDocTempName, String searchIsActive, String tenantCode) {
		StringBuilder queryParamSearch = new StringBuilder();

		if(StringUtils.isNotBlank(searchDocTempCode)) {
			params.put(MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(searchDocTempCode));
			queryParamSearch.append(" AND dt.doc_template_code like concat('%', :docTemplateCode ,'%' ) ");
		}
		
		if(StringUtils.isNotBlank(searchDocTempName)) {
			params.put(MsDocTemplate.DOCUMENT_TEMPLATE_NAME_HBM, StringUtils.upperCase(searchDocTempName));
			queryParamSearch.append(" AND dt.doc_template_name like concat('%', :docTemplateName ,'%' ) ");
		}
		
		if(StringUtils.isNotBlank(searchIsActive)) {
			params.put(ActiveAndUpdateableEntity.IS_ACTIVE_HBM, searchIsActive);
			queryParamSearch.append(" AND dt.is_active like concat('%', :isActive ,'%' ) ");
		}
		
		if(StringUtils.isNotBlank(tenantCode)) {
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
			queryParamSearch.append(" AND t.tenant_code = :tenantCode ");
		}
		
		return queryParamSearch;
	
	}
	
	@Override
	public List<DocumentTemplateBean> getListDocumentTemplate(String searchDocTempCode, String searchDocTempName, String searchIsActive,
			String tenantCode, int page, int pageSize) {
		Map<String, Object> params = new HashMap<>();
	
		params.put("codeLovParaf", GlobalVal.CODE_LOV_SIGN_TYPE_PRF);
		params.put("codeLovTtd", GlobalVal.CODE_LOV_SIGN_TYPE_TTD);
		params.put("codeLovMaterai", GlobalVal.CODE_LOV_SIGN_TYPE_SDT);
		
		PagingStartEndBean paging = PagingStartEndBean.fromPageNoAndSize(page, pageSize);
		params.put("start", paging.getStart());
		params.put("end", paging.getEnd());

		StringBuilder query = new StringBuilder();
		query
			.append(" WITH PARAF AS ( ")
			.append(" SELECT sl.id_doc_template, COUNT(sign_location) AS count_prf ")
			.append(" FROM ms_doc_template_sign_loc sl ")
			.append(" JOIN ms_doc_template AS dt ON sl.id_doc_template = dt.id_doc_template ")
			.append(" JOIN ms_lov AS ml ON sl.lov_sign_type = ml.id_lov AND ml.is_active ='1' ")
			.append(" WHERE ml.code = :codeLovParaf ")
			.append(" GROUP BY sl.id_doc_template ")
		.append(" ), ")
		.append(" TANDATANGAN as ( ")
			.append(" SELECT sl.id_doc_template, COUNT(sign_location) AS count_ttd ")
			.append(" FROM ms_doc_template_sign_loc sl ")
			.append(" JOIN ms_doc_template AS dt ON sl.id_doc_template = dt.id_doc_template ")
			.append(" JOIN ms_lov AS ml ON sl.lov_sign_type = ml.id_lov AND ml.is_active ='1' ")
			.append(" WHERE ml.code = :codeLovTtd ")
			.append(" GROUP BY sl.id_doc_template ")
		.append(" ), ")
		.append(" MATERAI as ( ")
			.append(" SELECT sl.id_doc_template, COUNT(sign_location) AS count_sdt ")
			.append(" FROM ms_doc_template_sign_loc sl ")
			.append(" JOIN ms_doc_template AS dt ON sl.id_doc_template = dt.id_doc_template ")
			.append(" JOIN ms_lov AS ml ON sl.lov_sign_type = ml.id_lov AND ml.is_active ='1' ")
			.append(" WHERE ml.code = :codeLovMaterai ")
			.append(" GROUP BY sl.id_doc_template ")
		.append(" ) ")
		.append(" SELECT \"idDocumentTemplate\", \"documentTemplateCode\",")
			.append(" \"documentTemplateName\", \"documentTemplateDescription\",")
			.append(" \"numberOfPage\", \"numberOfSign\", \"numberOfInitial\", \"isActive\", ")
			.append(" \"numberOfStampDuty\", \"paymentSignTypeCode\", \"paymentSignTypeDescription\", \"vendorCode\", \"isSequence\", \"useSignQr\", \"prioritySequence\" ")
		.append(" from ( ")
			.append(" SELECT CAST(dt.id_doc_template AS VARCHAR) as \"idDocumentTemplate\" , doc_template_code as \"documentTemplateCode\", ")
				.append(" doc_template_name as \"documentTemplateName\", doc_template_description as \"documentTemplateDescription\", ")
				.append(" CAST(COALESCE(number_of_page,0) AS VARCHAR) as \"numberOfPage\", CAST(COALESCE(count_ttd,0) AS VARCHAR) as \"numberOfSign\", ")
				.append(" CAST(COALESCE(count_prf,0) AS VARCHAR) as \"numberOfInitial\", CAST(COALESCE(count_sdt,0) AS VARCHAR) as \"numberOfStampDuty\", ")
				.append(" dt.is_active as \"isActive\", l.code AS \"paymentSignTypeCode\", l.description AS \"paymentSignTypeDescription\", ")
				.append(" (select vendor_code from ms_vendor where ms_vendor.id_ms_vendor = dt.id_ms_vendor) AS \"vendorCode\", dt.is_sequence AS \"isSequence\", ")
				.append(" dt.use_sign_qr AS \"useSignQr\", COALESCE(dt.priority_sequence, 0) AS \"prioritySequence\", ")
				
				.append(" ROW_NUMBER() OVER ( ORDER BY dt.is_active DESC, doc_template_code, doc_template_name) AS rownum ")
			.append(" FROM ms_doc_template dt ")
			.append(" JOIN ms_tenant as t on dt.id_ms_tenant = t.id_ms_tenant ")
			.append(" LEFT JOIN paraf ON paraf.id_doc_template = dt.id_doc_template ")
			.append(" LEFT JOIN tandatangan ON tandatangan.id_doc_template = dt.id_doc_template ")
			.append(" LEFT JOIN materai ON materai.id_doc_template = dt.id_doc_template")
			.append(" LEFT JOIN ms_lov as l ON l.id_lov = dt.lov_payment_sign_type")
			.append(" WHERE 1=1 ")
			.append(this.constructParamSearchListDocumentTemplate(params, searchDocTempCode, searchDocTempName, searchIsActive, tenantCode))
		.append(" ) tbl where rownum >= :start and rownum <= :end ");
		
		return this.managerDAO.selectForListString(DocumentTemplateBean.class, query.toString(), params, null);
	}

	@Override
	public BigInteger countListDocumentTemplate(String searchDocTempCode, String searchDocTempName, String searchIsActive, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		query
			.append(" SELECT count(*) ")
			.append(" FROM ms_doc_template dt ")
			.append(" JOIN ms_tenant as t on dt.id_ms_tenant = t.id_ms_tenant ")
			.append(" WHERE 1=1 ")
			.append(this.constructParamSearchListDocumentTemplate(params, searchDocTempCode, searchDocTempName, searchIsActive, tenantCode));
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public void insertDocumentTemplate (MsDocTemplate docTemplate) {
		docTemplate.setUsrCrt(MssTool.maskData(docTemplate.getUsrCrt()));
		this.managerDAO.insert(docTemplate);
		this.auditManager.auditAdd(docTemplate, auditInfoDT, docTemplate.getUsrCrt(), "");
	}

	@Override
	public void updateDocumentTemplate(MsDocTemplate docTemplate) {
		docTemplate.setUsrUpd(MssTool.maskData(docTemplate.getUsrUpd()));
		this.managerDAO.update(docTemplate);
		this.auditManager.auditAdd(docTemplate, auditInfoDT, docTemplate.getUsrUpd(), "");
	}
	
	@Override
	public MsDocTemplate getDocumentTemplateByCodeAndTenantCode(String docTemplateCode, String tenantCode) {
		if (StringUtils.isBlank(docTemplateCode)) {
			return null;
		}

		Object[][] queryParams = {
				{ MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(docTemplateCode)},
				{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}};

		Object[] result = (Object[]) this.managerDAO.selectOne(
				"select dt.idDocTemplate, dt.msLovPaymentSignType, dt.msTenant, dt.msVendor, "
				+ "dt.docTemplateCode, dt.docTemplateName, dt.docTemplateDescription, dt.numberOfPage, "
				+ "dt.isSequence, dt.height, dt.width, dt.useSignQr, dt.prioritySequence, "
				+ "dt.isActive, dt.usrCrt, dt.dtmCrt, dt.usrUpd, dt.dtmUpd "
				+ "from MsDocTemplate dt "
				+ "join dt.msTenant mt "
				+ "left join dt.msVendor mv "
				+ "where dt.docTemplateCode = :docTemplateCode "
				+ "and mt.tenantCode = :tenantCode",
				queryParams);

		if (result == null) {
			return null;
		}

		MsDocTemplate docTemplate = new MsDocTemplate();
		docTemplate.setIdDocTemplate((Long) result[0]);
		docTemplate.setMsLovPaymentSignType((MsLov) result[1]);
		docTemplate.setMsTenant((MsTenant) result[2]);
		docTemplate.setMsVendor((MsVendor) result[3]);
		docTemplate.setDocTemplateCode((String) result[4]);
		docTemplate.setDocTemplateName((String) result[5]);
		docTemplate.setDocTemplateDescription((String) result[6]);
		docTemplate.setNumberOfPage((Short) result[7]);
		docTemplate.setIsSequence((String) result[8]);
		docTemplate.setHeight((String) result[9]);
		docTemplate.setWidth((String) result[10]);
		docTemplate.setUseSignQr((String) result[11]);
		docTemplate.setPrioritySequence((Short) result[12]);
		docTemplate.setIsActive((String) result[13]);
		docTemplate.setUsrCrt((String) result[14]);
		docTemplate.setDtmCrt((Date) result[15]);
		docTemplate.setUsrUpd((String) result[16]);
		docTemplate.setDtmUpd((Date) result[17]);

		return docTemplate;
	}
	
	@Override
	public MsDocTemplate getDocumentTemplateByCodeTenantCodeAndVendorCode(String docTemplateCode, String tenantCode, String vendorCode) {
		Object[][] queryParams = { 
				{ MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(docTemplateCode)}, 
				{ MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)},
				{ "vendorCode", StringUtils.upperCase(vendorCode)}};
		
		return this.managerDAO.selectOne(
				"from MsDocTemplate dt "
				+" join fetch dt.msTenant mt "
				+" join fetch dt.msVendor mv "
				+" where dt.docTemplateCode = :docTemplateCode "
				+" and mt.tenantCode = :tenantCode "
				+" and mv.vendorCode = :vendorCode ", 
				queryParams);
	}
	
	@Override
	public List<MsDocTemplateSignLoc> getListSignLocationByTemplateCode(String documentTemplateCode) {
		Object[][] queryParams = {{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)}};
		return (List<MsDocTemplateSignLoc>) this.managerDAO.list(" from MsDocTemplateSignLoc sLoc "
				+ " join fetch sLoc.msDocTemplate docTemp "
				+ " left join fetch sLoc.msLovByLovSignerType mlSigner  "
				+ " join fetch sLoc.msLovByLovSignType mlSign "
				+ " where docTemp.docTemplateCode = :docTemplateCode ",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeAndIdTenant(String documentTemplateCode, long idTenant) {
		Object[][] queryParams = {{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)}, {MsTenant.ID_TENANT_HBM, idTenant}};
		List<Object[]> results = (List<Object[]>) this.managerDAO.list(
				"select sLoc, "
				+ "docTemp.idDocTemplate, docTemp.msLovPaymentSignType, docTemp.msTenant, docTemp.msVendor, "
				+ "docTemp.docTemplateCode, docTemp.docTemplateName, docTemp.docTemplateDescription, docTemp.numberOfPage, "
				+ "docTemp.isSequence, docTemp.height, docTemp.width, docTemp.useSignQr, docTemp.prioritySequence, "
				+ "docTemp.isActive, docTemp.usrCrt, docTemp.dtmCrt, docTemp.usrUpd, docTemp.dtmUpd, "
				+ "mlSigner, mlSign "
				+ "from MsDocTemplateSignLoc sLoc "
				+ "join sLoc.msDocTemplate docTemp "
				+ "join docTemp.msTenant tenant "
				+ "left join sLoc.msLovByLovSignerType mlSigner "
				+ "left join sLoc.msLovByLovSignType mlSign "
				+ "where docTemp.docTemplateCode = :docTemplateCode and tenant.idMsTenant = :idMsTenant",
				queryParams).get(GlobalKey.MAP_RESULT_LIST);

		if (results == null || results.isEmpty()) {
			return new ArrayList<>();
		}

		List<MsDocTemplateSignLoc> signLocations = new ArrayList<>();

		for (Object[] result : results) {
			MsDocTemplateSignLoc signLoc = (MsDocTemplateSignLoc) result[0];
			MsDocTemplate docTemplate = new MsDocTemplate();
			docTemplate.setIdDocTemplate((Long) result[1]);
			docTemplate.setMsLovPaymentSignType((MsLov) result[2]);
			docTemplate.setMsTenant((MsTenant) result[3]);
			docTemplate.setMsVendor((MsVendor) result[4]);
			docTemplate.setDocTemplateCode((String) result[5]);
			docTemplate.setDocTemplateName((String) result[6]);
			docTemplate.setDocTemplateDescription((String) result[7]);
			docTemplate.setNumberOfPage((Short) result[8]);
			docTemplate.setIsSequence((String) result[9]);
			docTemplate.setHeight((String) result[10]);
			docTemplate.setWidth((String) result[11]);
			docTemplate.setUseSignQr((String) result[12]);
			docTemplate.setPrioritySequence((Short) result[13]);
			docTemplate.setIsActive((String) result[14]);
			docTemplate.setUsrCrt((String) result[15]);
			docTemplate.setDtmCrt((Date) result[16]);
			docTemplate.setUsrUpd((String) result[17]);
			docTemplate.setDtmUpd((Date) result[18]);
			signLoc.setMsDocTemplate(docTemplate);
			signLoc.setMsLovByLovSignerType((MsLov) result[19]);
			signLoc.setMsLovByLovSignType((MsLov) result[20]);

			signLocations.add(signLoc);
		}

		return signLocations;
	}
	
	@Override
	public List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeTenantCode(String documentTemplateCode, String tenantCode) {
		Object[][] queryParams = {{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)}, {MsTenant.TENANT_CODE_HBM, tenantCode}};
		return (List<MsDocTemplateSignLoc>) this.managerDAO.list(" from MsDocTemplateSignLoc sLoc " 
				+ " join fetch sLoc.msDocTemplate docTemp "
				+ " join fetch docTemp.msTenant tenant"
				+ " join fetch sLoc.msLovByLovSignType mlSign "
				+ " where docTemp.docTemplateCode = :docTemplateCode and tenant.tenantCode = :tenantCode",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void insertSignLocation(MsDocTemplateSignLoc signLocation) {
		signLocation.setUsrCrt(MssTool.maskData(signLocation.getUsrCrt()));
		this.managerDAO.insert(signLocation);
		this.auditManager.auditAdd(signLocation, auditInfoSL, signLocation.getUsrCrt(), "");
	}
	
	@Override
	public void deleteSignLocation(List<MsDocTemplateSignLoc> signLocationList) {
		this.managerDAO.delete(signLocationList, signLocationList.size());
	}
	
	@Override
	public String generateDocumentId() {
		return this.getManagerDAO().getUUID().toUpperCase();
	}
	
	@Override
	public TrDocumentD insertDocumentDetail(TrDocumentD doc) {
		String documentId = this.getManagerDAO().getUUID().toUpperCase();
		doc.setDocumentId(documentId);
		doc.setUsrCrt(MssTool.maskData(doc.getUsrCrt()));
		this.managerDAO.insert(doc);
		return doc;
	}
	
	@Override
	public void updateDocumentDetail(TrDocumentD doc) {
		doc.setUsrUpd(MssTool.maskData(doc.getUsrUpd()));
		this.managerDAO.update(doc);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDetailNoRollBack(long idDocD, Date completedDate, String signStatus, Date currDate, String usrUpd, short totalSigned) {
		TrDocumentD doc = this.managerDAO.selectOne(TrDocumentD.class, idDocD);
		MsLov lov = daofactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGN_STATUS, signStatus);
		doc.setMsLovByLovSignStatus(null != lov ? lov : doc.getMsLovByLovSignStatus());
		doc.setDtmUpd(currDate);
		doc.setUsrUpd(MssTool.maskData(usrUpd));
		doc.setCompletedDate(completedDate);
		doc.setTotalSigned(totalSigned);
		
		this.managerDAO.update(doc);
	}

	@Override
	public TrDocumentD getDocumentDetailByDocId(String docId) {
		
		if (StringUtils.isBlank(docId)) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " join fetch dh.msOffice mo"
				+ " join fetch d.msTenant tenant "
				+ " join fetch d.msVendor vendor "
				+ " join fetch d.msLovByLovSignStatus signStatus "
				+ " left join fetch d.msLovByLovPaymentSignType signStatus "
				+ " left join fetch dh.msBusinessLine mb "
				+ " left join fetch mo.msRegion mr "
				+ " where d.documentId = :documentId ", 
				new Object[][] {{TrDocumentD.DOCUMENT_ID_HBM, StringUtils.upperCase(docId)}});
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentD getDocumentDetailByDocIdNewTrx(String docId) {
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " left join fetch dh.msOffice mo"
				+ " join fetch d.msTenant tenant "
				+ " join fetch d.msVendor vendor "
				+ " join fetch d.msLovByLovSignStatus signStatus "
				+ " left join fetch d.msLovByLovPaymentSignType signStatus "
				+ " left join fetch dh.msBusinessLine mb "
				+ " left join fetch mo.msRegion mr "
				+ " left join fetch dh.amMsuserByIdMsuserRequestBy "
				+ " left join fetch dh.amMsuserByIdMsuserCustomer "
				+ " join fetch dh.msLov "
				+ " join fetch dh.msTenant "
				+ " where d.documentId = :documentId ", 
				new Object[][] {{TrDocumentD.DOCUMENT_ID_HBM, StringUtils.upperCase(docId)}});
	}
	
	@Override
	public TrDocumentD getDocumentDetailStampingOnlyByDocId(String docId) {
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " join fetch dh.msOffice mo"
				+ " join fetch d.msTenant tenant "
				+ " join fetch d.msVendor vendor "
				+ " where d.documentId = :documentId ", 
				new Object[][] {{TrDocumentD.DOCUMENT_ID_HBM, StringUtils.upperCase(docId)}});
	}
	
	@Override
	public TrDocumentD getDocumentDetailById(long idDocInserted) {
		Object[][] queryParams = { 
				{ Restrictions.eq(TrDocumentD.ID_DOCUMENT_D_HBM, idDocInserted)}};
		return this.managerDAO.selectOne(TrDocumentD.class, queryParams);
	}
	
	@Override
	// TODO: Should be removed, would cause error even in normal cases
	public TrDocumentD getDocumentDetailByDocumentHeaderId(long documentHeaderId) {
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " where dh.idDocumentH = :idDocumentH ", 
				new Object[][] {{TrDocumentH.ID_DOCUMENT_H_HBM, documentHeaderId}});
	}
	
	@Override
	public List<TrDocumentD> getListDocumentDetailByDocumentHeaderId(long documentHeaderId) {
		Object[][] queryParams = {{TrDocumentH.ID_DOCUMENT_H_HBM, documentHeaderId}};
		
		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
				+ "join fetch docD.trDocumentH docH "
				+ "join fetch docD.msTenant tenant "
				+ "join fetch docD.msVendor vendor "
				+ "left join fetch docD.msDocTemplate docTemplt "
				+ "where docH.idDocumentH = :idDocumentH ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void insertDocumentHeader(TrDocumentH documentH) {
		documentH.setUsrCrt(MssTool.maskData(documentH.getUsrCrt()));
		this.managerDAO.insert(documentH);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentHeaderNewTrx(TrDocumentH documentH) {
		documentH.setUsrCrt(MssTool.maskData(documentH.getUsrCrt()));
		this.managerDAO.insert(documentH);
	}
	
	@Override
	public TrDocumentH getDocumentHeaderByRefNo(String refNo) {
		if (StringUtils.isBlank(refNo))
			return null;
	
		return this.managerDAO.selectOne(
				"from TrDocumentH ta "
				+ "join fetch ta.msTenant mt "
				+ "join fetch ta.amMsuserByIdMsuserCustomer am "
				+ "join fetch ta.msOffice mo "
				+ "where ta.refNumber = :refNumber and mo.isActive ='1'  and am.isActive ='1' ", 
						new Object[][] {{TrDocumentH.REF_NUMBER_HBM, StringUtils.upperCase(refNo)}});
	}
	
	@Override
	public TrDocumentH getDocumentHeaderByRefNoAndTenantCode(String refNo, String tenantCode) {
		Object[][] queryParams = {
				{TrDocumentH.REF_NUMBER_HBM, refNo},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from TrDocumentH docH "
				+ "join fetch docH.msTenant t "
				+ "where docH.refNumber = :refNumber and t.tenantCode = :tenantCode ", queryParams);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentH getDocumentHeaderByRefNoAndTenantCodeNewTran(String refNo, String tenantCode) {
		Object[][] queryParams = {
				{TrDocumentH.REF_NUMBER_HBM, refNo},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return this.managerDAO.selectOne(
				"from TrDocumentH docH "
				+ "join fetch docH.msTenant t "
				+ "where docH.refNumber = :refNumber and t.tenantCode = :tenantCode ", queryParams);
	}
	
	@Override
	public List<TrDocumentH> getListDocumentHeaderByProsesMeterai(Short prosesMeterai) {
		Object[][] queryParams = {{"prosesMaterai", prosesMeterai}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "join fetch docH.msTenant mt "
				+ "join fetch docH.amMsuserByIdMsuserCustomer am "
				+ "join fetch docH.msOffice mo "
				+ "where docH.prosesMaterai = :prosesMaterai and mo.isActive ='1'  and am.isActive ='1' "
				+ "order by docH.idDocumentH ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public List<TrDocumentH> getListDocumentHeaderByCallbackProcess(Short callbackProcess) {
		Object[][] queryParams = {{TrDocumentH.CALLBACK_PROCESS_HBM, callbackProcess}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "join fetch docH.amMsuserByIdMsuserCustomer am "
				+ "join fetch docH.msOffice mo "
				+ "where docH.callbackProcess = :callbackProcess and mo.isActive ='1' and am.isActive ='1' ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void insertDocumentDetailSign(TrDocumentDSign documentDSign) {
		documentDSign.setUsrCrt(MssTool.maskData(documentDSign.getUsrCrt()));
		this.managerDAO.insert(documentDSign);
	}

	@Override
	public BigInteger countStampNeededByTemplateCode(String documentTemplateCode, long idTenant) {
		Object[][] param = new Object[][] {
			{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode) }, 
			{MsTenant.ID_TENANT_HBM, idTenant}};
		
		StringBuilder query = new StringBuilder()
			.append("select count(1) ")
			.append("from ms_doc_template dt ")
			.append("join ms_doc_template_sign_loc sl on sl.id_doc_template = dt.id_doc_template ")
			.append("join ms_lov lov on lov.id_lov = sl.lov_sign_type ")
			.append("where lov.lov_group = '"+GlobalVal.LOV_GROUP_SIGN_TYPE+"' ")
				.append("and lov.code = '"+GlobalVal.CODE_LOV_SIGN_TYPE_SDT+"' ")
				.append("and dt.doc_template_code = :docTemplateCode and dt.id_ms_tenant = :idMsTenant");
			
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), param);
	}

	@Override
	public List<Map<String, Object>> getListStampLocationByTemplateCode(String documentTemplateCode, long idTenant) {
		
		Object[][] param = new Object[][] {
			{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)}, 
			{MsTenant.ID_TENANT_HBM, idTenant}};
		
		StringBuilder query = new StringBuilder()
				.append(" select sign_location, sign_page ")
				.append(" from ms_doc_template_sign_loc sl ")
				.append(" join ms_doc_template dt on sl.id_doc_template = dt.id_doc_template ")
				.append(" join ms_lov lov on sl.lov_sign_type = lov.id_lov ")
				.append(" where lov.lov_group = '"+GlobalVal.LOV_GROUP_SIGN_TYPE+"' ")
					.append(" and lov.code = '"+GlobalVal.CODE_LOV_SIGN_TYPE_SDT+"' ")
					.append(" and doc_template_code = :docTemplateCode ")
					.append(" and dt.id_ms_tenant = :idMsTenant ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), param);
	}

	@Override
	public BigInteger countOtherDocDetailNeedSign(long idDocumentH, long idDocumentD, long idMsUser) {
		
		StringBuilder query = new StringBuilder();
		
		Object[][] params = new Object[][] {
			{TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH},
			{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD},
			{AmMsuser.ID_MS_USER_HBM, idMsUser}
		};
		
		query
			.append("select count(1) ")
			.append("from tr_document_h dh ")
			.append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			.append("join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("where dh.id_document_h = :idDocumentH and dds.id_document_d != :idDocumentD ")
			.append("and dds.id_ms_user = :idMsUser and dds.sign_date is null");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDocDetailNeedSign(long idDocumentH, long idMsUser) {
		StringBuilder query = new StringBuilder();
		
		Object[][] params = new Object[][] {
			{TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH},
			{AmMsuser.ID_MS_USER_HBM, idMsUser}
		};
		
		query
			.append("select distinct on (document_id) document_id, ")
			.append("case when dt.doc_template_name is null then dd.document_name ")
			.append("else dt.doc_template_name end, ")
			.append("dh.ref_number, mv.vendor_code ")
			.append("from tr_document_h dh ")
			.append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			.append("left join ms_doc_template dt on dt.id_doc_template = dd.id_ms_doc_template ")
			.append("join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("join ms_vendor mv on dd.id_ms_vendor = mv.id_ms_vendor ")
			.append("where dh.id_document_h = :idDocumentH ")
			.append("and dds.id_ms_user = :idMsUser and dds.sign_date is null");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public TrDocumentH getEarliestAgreement(AmMsuser user) {
		StringBuilder query = new StringBuilder();
		query.append(" select dh.id_document_h ")
			 .append(" from tr_document_h dh ")
			 .append(" join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			 .append(" join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			 .append(" where dds.id_ms_user = :idMsUser ")
			 .append(" order by dh.dtm_crt asc ")
			 .append(" limit 1 ");
		
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		BigInteger id = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == id) {
			return null;
		}
		return this.managerDAO.selectOne(TrDocumentH.class, id.longValue());
	}
	
	@Override
	public List<ActivationDocumentBean> getActivationDocumentByDocHAndUser(TrDocumentH docH, AmMsuser user) {
		StringBuilder query = new StringBuilder();
		
		Map<String, Object> params = new HashMap<>();
		params.put("idDocH", null != docH ? docH.getIdDocumentH() : 0);
		
		query.append("select distinct on (document_id) document_id as \"documentId\", h.ref_number as \"refNumber\", dt.doc_template_name as \"docTemplateName\", ")
			 .append("d.total_sign as \"totalSign\", d.total_signed as \"totalSigned\", dt.doc_template_code as \"documentTemplateCode\", ")
			 .append("transaction_id as \"transactionIds\", total_materai as \"totalMeterai\", total_stamping as \"totalStamped\", d.document_sdt_link as \"docLink\" ")
			 .append("from tr_document_d d ")
			 .append("join tr_document_d_sign as dsign on d.id_document_d = dsign.id_document_d ")
			 .append("join tr_document_h as h on d.id_document_h = h.id_document_h ")
			 .append("join ms_doc_template as dt on dt.id_doc_template = d.id_ms_doc_template ")
			 .append("where h.id_document_h = :idDocH ");
		
		if (null != user) {
			query.append("and dsign.id_ms_user = :idMsUser ");
			params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		}
		return this.managerDAO.selectForListString(ActivationDocumentBean.class, query.toString(), params, null);
	}

	@Override
	public void updateDocumentH(TrDocumentH documentH) {
		documentH.setUsrUpd(MssTool.maskData(documentH.getUsrUpd()));
		this.managerDAO.update(documentH);
	}

	@Override
	public List<TrDocumentDSign> getDocumentDSignByIdDocumentDAndIdUser(Long idDocumentD, Long idUser) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT id_document_d_sign ")
			 	.append("FROM tr_document_d_sign ")
			 	.append("WHERE id_document_d = :idDocumentD AND id_ms_user = :idMsUser");
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		params.put(AmMsuser.ID_MS_USER_HBM, idUser);
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		List<TrDocumentDSign> lDocDSign = new ArrayList<>();
		
		for(Map<String, Object> id : result) {
			BigInteger idDocD = (BigInteger) id.get("d0");
			TrDocumentDSign docDSign = this.managerDAO.selectOne(TrDocumentDSign.class, idDocD.longValue());
			lDocDSign.add(docDSign);
		}
		
		return lDocDSign;
	}

	@Override
	public List<TrDocumentDSign> getDocumentDSignByIdDocumentDAndIdUserNewTrx(Long idDocumentD, Long idUser) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT id_document_d_sign ")
			 	.append("FROM tr_document_d_sign ")
			 	.append("WHERE id_document_d = :idDocumentD AND id_ms_user = :idMsUser");
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		params.put(AmMsuser.ID_MS_USER_HBM, idUser);
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		List<TrDocumentDSign> lDocDSign = new ArrayList<>();
		
		for(Map<String, Object> id : result) {
			BigInteger idDocD = (BigInteger) id.get("d0");
			TrDocumentDSign docDSign = this.managerDAO.selectOne(TrDocumentDSign.class, idDocD.longValue());
			lDocDSign.add(docDSign);
		}
		
		return lDocDSign;
	}
	
	@Override
	public boolean isUserSignedDocument(String documentId, Long idUser) {
		StringBuilder query = new StringBuilder();
		query.append(" SELECT id_document_d_sign ")
			 	.append(" FROM tr_document_d_sign dsign ")
			 	.append(" JOIN tr_document_d  detail on dsign.id_document_d = detail.id_document_d ")
			 	.append(" WHERE document_id = :documentId AND dsign.id_ms_user = :idMsUser ")
			 	.append(" AND sign_date is null ");
		Map<String, Object> params = new HashMap<>();
		params.put("documentId", documentId);
		params.put(AmMsuser.ID_MS_USER_HBM, idUser);
		
		List<Map<String, Object>> resultList = this.managerDAO.selectAllNativeString(query.toString(), params);

		return resultList.isEmpty();
	}
	
	@Override
	public void updateDocumentDSign(TrDocumentDSign documentDSign) {
		documentDSign.setUsrUpd(MssTool.maskData(documentDSign.getUsrUpd()));
		this.managerDAO.update(documentDSign);
	}

	@Override
	public int countSignedDocumentDSign(Long idDocumentD, Long idUser) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT COUNT(*) ")
			 	.append("FROM tr_document_d_sign ")
			 	.append("WHERE id_document_d = :idDocumentD AND id_ms_user = :idMsUser AND sign_date IS NOT NULL");
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		params.put(AmMsuser.ID_MS_USER_HBM, idUser);
		return 0;
	}


	private StringBuilder buildConditionalParam(String[] paramsInquiry, Map<String, Object> paramsQuery, String callerId, String queryType, String msg, boolean isReadOffice) {
		StringBuilder conditionalParam = new StringBuilder();
		AuditContext audit = new AuditContext (callerId);
		
		boolean userMustExists = true;
		if (GlobalVal.INQUIRY_TYPE_LIST_NON_CUST.equals(queryType)) {
			userMustExists = false;
		}
		AmMsuser user = userValidatorLogic.validateGetUserByEmailv2(callerId, userMustExists, audit);
		
		// Tenant code
		if (StringUtils.isNotBlank(paramsInquiry[0])) {
			conditionalParam.append(" and mt.tenant_code = :tenantCode ");
			paramsQuery.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(paramsInquiry[0]));
		}
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			// Status need Sign
			conditionalParam.append(" and dd.lov_sign_status = :idLov ");
			paramsQuery.put(MsLov.ID_LOV_HBM, daofactory.getLovDao().getMsLovByCode(GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN).getIdLov());
				
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());

			// Office code
			if (StringUtils.isNotBlank(paramsInquiry[1]) && isReadOffice) {
				conditionalParam.append(" and office.office_code = :officeCode ");
				paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
			}
			
		} else if (GlobalVal.INQUIRY_TYPE_LIST_CUST.equals(queryType)) {
			// Status
			if (StringUtils.isNotBlank(paramsInquiry[3])) {
				conditionalParam.append(" and dd.lov_sign_status = :idLov ");
				paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
			}
			
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
			
			// Office code
			if (StringUtils.isNotBlank(paramsInquiry[1])) {
				conditionalParam.append(" and  office.office_code = :officeCode ");
				paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
			}
		} else if (GlobalVal.INQUIRY_TYPE_LIST_NON_CUST.equals(queryType)) {
			// Office code
			if (StringUtils.isNotBlank(paramsInquiry[1])) {
				conditionalParam.append(" and office.office_code = :officeCode ");
				paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
			}
			
			// Customer name
			if (StringUtils.isNotBlank(paramsInquiry[2])) {
				conditionalParam.append(" and usercust.full_name like :fullName ");
				paramsQuery.put(AmMsuser.FULLNAME_HBM, "%" + StringUtils.upperCase(paramsInquiry[2]) + "%");
			}
				
			// Status
			if (StringUtils.isNotBlank(paramsInquiry[3])) {
				conditionalParam.append(" and dd.lov_sign_status = :idLov ");
				paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
			}
			
			// Ref number
			if (StringUtils.isNotBlank(paramsInquiry[4])) {
				conditionalParam.append(" and upper(dh.ref_number) like :refNumber ");
				paramsQuery.put(TrDocumentH.REF_NUMBER_HBM, "%" + StringUtils.upperCase(paramsInquiry[4]) + "%");
			} else {
				// Request start date
				// Filter request date hanya dijalankan jika filter ref number kosong
				if (StringUtils.isNotBlank(paramsInquiry[5])) {
					conditionalParam.append(" and dd.request_date >= :requestDateStart ");
					paramsQuery.put("requestDateStart", MssTool.formatStringToDate(paramsInquiry[5] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append(" and dd.request_date >= date_trunc('month', now()) ");
				}
				
				// Request end date 
				if (StringUtils.isNotBlank(paramsInquiry[6])) {
					conditionalParam.append(" and dd.request_date <= :requestDateEnd ");
					paramsQuery.put("requestDateEnd", MssTool.formatStringToDate(paramsInquiry[6] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append("and dd.request_date <= now() ");
				}
				
				// Complete start date
				if (StringUtils.isNotBlank(paramsInquiry[7])) {
					conditionalParam.append(" and dd.completed_date >= :completeDateStart ");
					paramsQuery.put("completeDateStart",  MssTool.formatStringToDate(paramsInquiry[7] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
				
				// Complete end date
				if (StringUtils.isNotBlank(paramsInquiry[8])) {
					conditionalParam.append(" and dd.completed_date <= :completeDateEnd ");
					paramsQuery.put("completeDateEnd", MssTool.formatStringToDate(paramsInquiry[8] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
			}
			
			// Doc type
			if (StringUtils.isNotBlank(paramsInquiry[11])) {
				conditionalParam.append(" and dh.lov_doc_type = :docType ");
				paramsQuery.put("docType", Long.valueOf(paramsInquiry[11]));
			}
			
			// Region code
			if (StringUtils.isNotBlank(paramsInquiry[12])) {
				conditionalParam.append(" and region.region_code = :regionCode ");
				paramsQuery.put("regionCode", StringUtils.upperCase(paramsInquiry[12]));
			}
			
			// Proses Materai
			if (msg != null) {
			  if (StringUtils.isNotBlank(paramsInquiry[13])) {
			 	if(paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_NOT_STARTED)) {
					conditionalParam.append(" and dh.proses_materai = 0 ");
				} 
				else if(paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_FAILED)) {
					conditionalParam.append(" and dh.proses_materai in (1,51,321,521,61)");
				} 
				else if(paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_SUCCESS)) {
					conditionalParam.append(" and dh.proses_materai in (3,53,323,523,63)");
			    } 
				else if(paramsInquiry[13].equals(GlobalVal.HASIL_STAMPING_IN_PROGRESS)) {
					conditionalParam.append(" and dh.proses_materai in (2,52,322,522,5,55,325,525,64,65,62)");
			    } 
			  }
			}
			
		} else if (GlobalVal.INQUIRY_TYPE_LIST_BM_MF.equals(queryType)) {
			// id user
			paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());

			// Office code
			if (StringUtils.isNotBlank(paramsInquiry[1])) {
				conditionalParam.append(" and  office.office_code = :officeCode ");
				paramsQuery.put(MsOffice.OFFICE_CODE_HBM,  StringUtils.upperCase(paramsInquiry[1]));
			}
						
			// Customer name
			if (StringUtils.isNotBlank(paramsInquiry[2])) {
				conditionalParam.append(" and usercust.full_name like :fullName ");
				paramsQuery.put(AmMsuser.FULLNAME_HBM, "%" + StringUtils.upperCase(paramsInquiry[2]) + "%");
			}
							
			// Status
			if (StringUtils.isNotBlank(paramsInquiry[3])) {
				conditionalParam.append(" and dd.lov_sign_status = :idLov ");
				paramsQuery.put(MsLov.ID_LOV_HBM, Long.valueOf(paramsInquiry[3]));
			}
						
			// Ref number
			if (StringUtils.isNotBlank(paramsInquiry[4])) {
				conditionalParam.append(" and upper(dh.ref_number) like :refNumber ");
				paramsQuery.put(TrDocumentH.REF_NUMBER_HBM, "%" + StringUtils.upperCase(paramsInquiry[4]) + "%");
			} else {
				// Request start date
				if (StringUtils.isNotBlank(paramsInquiry[5])) {
					conditionalParam.append(" and dd.request_date >= :requestDateStart ");
					paramsQuery.put("requestDateStart", MssTool.formatStringToDate(paramsInquiry[5] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append(" and dd.request_date >= date_trunc('month', now()) ");
				}
							
				// Request end date 
				if (StringUtils.isNotBlank(paramsInquiry[6])) {
					conditionalParam.append(" and dd.request_date <= :requestDateEnd ");
					paramsQuery.put("requestDateEnd", MssTool.formatStringToDate(paramsInquiry[6] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				} else {
					conditionalParam.append("and dd.request_date <= now() ");
				}
							
				// Complete start date
				if (StringUtils.isNotBlank(paramsInquiry[7])) {
					conditionalParam.append(" and dd.completed_date >= :completeDateStart ");
					paramsQuery.put("completeDateStart", MssTool.formatStringToDate(paramsInquiry[7] + GlobalVal.SOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
							
				// Complete end date
				if (StringUtils.isNotBlank(paramsInquiry[8])) {
					conditionalParam.append(" and dd.completed_date <= :completeDateEnd ");
					paramsQuery.put("completeDateEnd", MssTool.formatStringToDate(paramsInquiry[8] + GlobalVal.EOD_TIME, GlobalVal.DATE_TIME_FORMAT_SEC));
				}
			}
						
			// Doc type
			if (StringUtils.isNotBlank(paramsInquiry[11])) {
				conditionalParam.append(" and dh.lov_doc_type = :docType ");
				paramsQuery.put("docType", Long.valueOf(paramsInquiry[11]));
			}
						
			// Region code
			if (StringUtils.isNotBlank(paramsInquiry[12])) {
				conditionalParam.append(" and region.region_code = :regionCode ");
				paramsQuery.put("regionCode", StringUtils.upperCase(paramsInquiry[12]));
			}
		}
		
		// Is Active
		
		// untuk /s/inquiryNormal
		if (paramsInquiry.length >= 15) {
			if (StringUtils.isNotBlank(paramsInquiry[14])) {
				conditionalParam.append(" and dh.is_active = :isActive ");
				paramsQuery.put("isActive", paramsInquiry[14]);
			}
		} else {
			// selain /s/inquiryNormal
			conditionalParam.append(" and dh.is_active = '1' ");
		}
		
		return conditionalParam;
	}
	
	@Override
	public List<Map<String, Object>> getListInquiryDocument(String[] paramsInquiry, String callerId, String queryType, String msg, boolean isReadOffice) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		// Params 9: start row, Params 10: end row
		if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
			paramsQuery.put(start, Integer.valueOf(paramsInquiry[9]));
			paramsQuery.put(end, Integer.valueOf(paramsInquiry[10]));
		}
		
		/*
			Query inquiry document untuk non-customer membutuhkan waktu > 3 menit untuk selesai. 
			Query inquiry document untuk inbox aman
			Link untuk contoh:
			/embed/inquiry?msg=uCJVQ%2FPggDtKKd%2Beo20mBAiTYhlhURGxbHk3uDX9mYbe1t4a7v2Iitgk2PzUt2y4loFd1VhjX2gbs0u4sZ6URdvDRILCmBTQjw%2B%2Fm9C%2Bum0%3D&isMonitoring=1&isHO=1
			
			Sementara join ke ms_doc_template di-comment, nama dokumen di-hardcode.
		*/
		StringBuilder conditionalParam = this.buildConditionalParam(paramsInquiry, paramsQuery, callerId, queryType, msg, isReadOffice);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT dds.id_document_d_sign, dds.id_ms_user, dd.id_document_d , dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  case when dd.id_ms_doc_template is not null then dt.doc_template_name else dd.document_name end AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.total_materai, dd.total_stamping, dh.automatic_stamping_after_sign AS \"status_otomatis_stamping\", ")
			.append("           dh.proses_materai AS \"status_proses_materai\" , ms_vendor.vendor_code AS \"vendor_code\", dd.signing_process AS \"signing_process\", ")
			.append("           dh.is_active AS \"is_active\", ")
			.append("           case when dh.automatic_stamping_after_sign != '1' and dh.proses_materai = 0 and dd.total_sign = dd.total_signed then '1' ")
			.append("           else '0' end AS \"can_start_stamp\", ")
			.append("           case when dd.priority_sequence is null then 32767 ") 
			.append("               when dd.priority_sequence = 0 then 32767 ")
			.append("               else dd.priority_sequence end as priority_sequence ")
			.append("    FROM tr_document_d dd ")
			.append("    JOIN LATERAL ( ")
					.append(" select dds.* from tr_document_d_sign dds ")
					// .append(" join ms_lov at on at.id_lov = dds.lov_autosign ")
					.append(" where dds.id_document_d = dd.id_document_d ");
		
				if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)
						|| GlobalVal.INQUIRY_TYPE_LIST_BM_MF.equals(queryType)
						|| GlobalVal.INQUIRY_TYPE_LIST_CUST.equals(queryType)) {
					query.append(" and dds.id_ms_user = :idMsUser ");
				}
				// if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)){
				// 	query.append(" and dds.sign_date is null ")
				// 	.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
				// }
					query.append(" limit 1 ")
			.append("    ) dds on true ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ");

		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ");
		}

		query.append(" WHERE 1 = 1 ");
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.
				append(" and dds.sign_date is null ")
				.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
		}
		
		query
			.append(conditionalParam)
			.append(" ) ")
				.append(" SELECT * FROM ( ")
					.append(" SELECT DISTINCT ON (id_document_d) ROW_NUMBER() OVER(ORDER BY date_trunc('minute', request_date), ref_number, priority_sequence) AS row, ")
					.append(" ref_number, doc_type_name, doc_name, customer_name, ")
					.append(" TO_CHAR(request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS request_date, ")
					.append(" TO_CHAR(completed_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS completed_date, document_id, ")
					.append(" CONCAT(countSigner - COALESCE(countSignNotDone,'0'), ' / ', COALESCE(countSigner,'0')) AS total_signed, ")
					.append(" ddsCTE.id_document_d,  office_name, region_name, sign_stat, ")
					.append(" concat(COALESCE(total_stamping, '0'), '/', COALESCE(total_materai, '0')), ")
					.append(" status_otomatis_stamping, ")
					.append(" status_proses_materai, vendor_code, signing_process, is_active, can_start_stamp ")
					.append(" FROM ddsCTE ")
					.append(" JOIN LATERAL ( ")
						.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
							.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NULL) AS countSignNotDone ")
						.append(" FROM tr_document_d_sign dds ")
						.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
						.append(" GROUP BY dds.id_document_d ")
					.append(" ) countSign ON TRUE ")
				.append(" ) as a ");
			
			// Params 9: start row, Params 10: end row
			if (!"0".equals(paramsInquiry[9]) && !"0".equals(paramsInquiry[10])) {
				query.append(" where a.row between :start and :end ")
					.append("order by row ");
			}
		
		return this.managerDAO.selectAllNativeString(query.toString(), paramsQuery);
	}

	@Override
	public long countListInquiryDocument(String[] paramsInquiry, String callerId, String queryType, String msg, boolean isReadOffice) {
		Map<String, Object> paramsQuery = new HashMap<>();
		
		StringBuilder conditionalParam = this.buildConditionalParam(paramsInquiry, paramsQuery, callerId, queryType, msg, isReadOffice);
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT dds.id_document_d_sign, dds.id_ms_user, dd.id_document_d , dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  dt.doc_template_name AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat ")
			.append("    FROM   tr_document_d dd ")
			.append("    JOIN LATERAL ( ")
					.append(" select dds.* from tr_document_d_sign dds ")
					// .append(" join ms_lov at on at.id_lov = dds.lov_autosign ")
					.append(" where dds.id_document_d = dd.id_document_d ");
		
				if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)
						|| GlobalVal.INQUIRY_TYPE_LIST_BM_MF.equals(queryType)
						|| GlobalVal.INQUIRY_TYPE_LIST_CUST.equals(queryType)) {
					query.append(" and dds.id_ms_user = :idMsUser ");
				}
				// if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)){
				// 	query.append(" and dds.sign_date is null ")
				// 	.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
				// }
					query.append(" limit 1 ")
			.append("    ) dds on true ")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ");

		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.append("JOIN ms_lov at on at.id_lov = dds.lov_autosign ");
		}

		query.append(" WHERE 1 = 1 ");
		
		if (GlobalVal.INQUIRY_TYPE_INBOX.equals(queryType)) {
			query.
				append(" and dds.sign_date is null ")
				.append(" and at.code = '" + GlobalVal.CODE_LOV_MANUALSIGN + "' ");
		}
		
		query
			.append(conditionalParam)
			.append(" ) ")
				.append(" SELECT COUNT (DISTINCT (ddsCTE.id_document_d))")
				.append(" FROM ddsCTE ")
				.append(" JOIN LATERAL ( ")
					.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
						.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NOT NULL) AS countSigned ")
					.append(" FROM tr_document_d_sign dds ")
					.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
					.append(" GROUP BY dds.id_document_d ")
				.append(" ) countSign ON TRUE ");
	
		
		return ((BigInteger) this.managerDAO.selectOneNativeString(query.toString(), paramsQuery)).longValue();
	}

	@Override
	public List<Map<String, Object>> getDocumentSignerList(String documentId) {
		StringBuilder query = new StringBuilder();
		query
			.append(" SELECT * FROM (select distinct on (vuser.signer_registered_email) signer_registered_email, COALESCE(lovds.description, 'Signer') AS \"description\", mu.full_name, vuser.phone_bytea, ")
				.append(" case when sign_date is null then ( ")
					.append(" select lov.description ")
					.append(" from ms_lov lov ")
					.append(" where lov.lov_group = '"+GlobalVal.LOV_GROUP_SIGN_STATUS+"' ")
					.append(" and lov.code = '"+GlobalVal.CODE_LOV_SIGN_STATUS_NEED_SIGN+"' ") 
				.append(") else 'Signed' end as sign_status, ")
				.append("to_char(dds.sign_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') as sign_date , dds.seq_no, vuser.email_service ")
			.append("from tr_document_d dd ")
			.append("join tr_document_d_sign dds on dd.id_document_d = dds.id_document_d ")
			    .append("left join lateral (Select lovds.description from ms_lov lovds ")
			    .append("where dds.lov_signer_type = lovds.id_lov ) lovds ON TRUE ")
			.append("join am_msuser mu on dds.id_ms_user = mu.id_ms_user ")
			.append("join ms_vendor_registered_user vuser on vuser.id_ms_user = mu.id_ms_user and vuser.id_ms_vendor = dd.id_ms_vendor ")
			.append("join am_user_personal_data upd on mu.id_ms_user = upd.id_ms_user ")
			.append("where dd.document_id = :documentId) sub ")
			.append("ORDER BY seq_no ASC ");
	
		
		List<Map<String, Object>> resultList = this.managerDAO.selectAllNativeString(query.toString(),
				new Object[][] {{ TrDocumentD.DOCUMENT_ID_HBM, documentId }});
		for(Map<String, Object> mapResult : resultList) {
			String phoneRaw = personalDataEncLogic.decryptToString((byte[]) mapResult.get("d3"));
			mapResult.put("d3",phoneRaw);  //decrypt phone user
		}
		return resultList;
	}

	@Override
	public TrDocumentD getLatestDocumentDetailBySignerLoginId(String loginId) {
		StringBuilder query = new StringBuilder();
		query
			.append(" select dd.id_document_d ")
			.append(" from tr_document_d dd ")
			.append(" join tr_document_d_sign dds on dd.id_document_d = dds.id_document_d ")
			.append(" join am_msuser mu on dds.id_ms_user = mu.id_ms_user ")
			.append(" where login_id = :loginId ")
			.append(" order by dds.dtm_crt desc ")
			.append(" limit 1 ");
		
		BigInteger idDocumentD = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(),
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
		
		if (null == idDocumentD) {
			return null;
		}
		
		return this.managerDAO.selectOne("select d from TrDocumentD d "
										 + "join fetch d.msTenant "
										 + "join fetch d.msVendor "
										 + "join fetch TrDocumentDSign sign on sign.trDocumentD = d "
										 + "join fetch AmMsuser u on u = sign.amMsuser "
										 + "where d.idDocumentD = :idDocumentD", 
								new Object[][] {{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD.longValue()}});
	}

	@Override
	public List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeAndLovSignType(String documentTemplateCode,
			String signTypeCode) {
		Object[][] queryParams = {
				{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)},
				{MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_SIGN_TYPE},
				{"signTypeCode", StringUtils.upperCase(signTypeCode)}
		};
		return (List<MsDocTemplateSignLoc>) this.managerDAO.list(" from MsDocTemplateSignLoc sLoc " 
				+ " join fetch sLoc.msDocTemplate docTemp "
				+ " left join fetch sLoc.msLovByLovSignerType mlSigner  "
				+ " join fetch sLoc.msLovByLovSignType mlSign "
				+ " where docTemp.docTemplateCode = :docTemplateCode and mlSign.lovGroup = :lovGroup and mlSign.code = :signTypeCode ",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<String> getListRefNumByLoginId(String loginId) {
		StringBuilder query = new StringBuilder();
		query.append("select distinct(h.ref_number) as \"refNumber\" from tr_document_h h ")
			 .append("join tr_document_d d on d.id_document_h = h.id_document_h ")
			 .append("join tr_document_d_sign ds on ds.id_document_d = d.id_document_d ")
			 .append("join am_msuser u on u.id_ms_user = ds.id_ms_user ")
			 .append("where u.login_id = :loginId ");
		
		Object[][] param = {{"loginId", StringUtils.upperCase(loginId)}};
		
		List<TrDocumentH> listDocH = this.managerDAO.selectForListString(TrDocumentH.class, query.toString(), param,  null);
		List<String> result = new ArrayList<>();
		for (TrDocumentH docH : listDocH) {
			result.add(docH.getRefNumber());
		}
		
		return result;
	}

	@Override
	public void insertDocumentDetailSdt(TrDocumentDStampduty documentDSdt) {
		documentDSdt.setUsrCrt(MssTool.maskData(documentDSdt.getUsrCrt()));
		this.managerDAO.insert(documentDSdt);
	}
	
	@Override
	public List<TrDocumentDStampduty> getDocumentStampDutyByIdDocumentD(Long idDocumentD) {
		Object[][] queryParams = {{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD}};
		
		return (List<TrDocumentDStampduty>) this.managerDAO.list(
				"from TrDocumentDStampduty docSdt "
				+ "join fetch docSdt.trDocumentD docD "
				+ "left join fetch docSdt.trStampDuty sdt "
				+ "where docD.idDocumentD = :idDocumentD "
				+ "order by docSdt.signPage, docSdt.seqNo ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public void updateDocumentDetailSdt(TrDocumentDStampduty documentDSdt) {
		documentDSdt.setUsrUpd(MssTool.maskData(documentDSdt.getUsrUpd()));
		this.managerDAO.update(documentDSdt);
	}

	@Override
	public TrDocumentD getEarliestUnsignedDocumentBySignerLoginId(String loginId) {
		
		StringBuilder query = new StringBuilder();
		query
			.append(" select dds.id_document_d ")
			.append(" from tr_document_d_sign dds ")
			.append(" join am_msuser mu on dds.id_ms_user = mu.id_ms_user ")
			.append(" where mu.login_id = :loginId ")
			.append(" and dds.sign_date is null ")
			.append(" order by id_document_d_sign asc limit 1 ");
		
		BigInteger idDocumentD = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), 
				new Object[][] {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }});
		
		if (null == idDocumentD) {
			return null;
		}
		return this.getDocumentDetailById(idDocumentD.longValue());
	}

	@Override
	public void insertDocumentHStampdutyError(TrDocumentHStampdutyError documentHStampdutyError) {
		documentHStampdutyError.setUsrCrt(MssTool.maskData(documentHStampdutyError.getUsrCrt()));
		this.managerDAO.insert(documentHStampdutyError);
	}

	@Override
	public void updateDocumentHStampdutyError(TrDocumentHStampdutyError documentHStampdutyError) {
		documentHStampdutyError.setUsrUpd(MssTool.maskData(documentHStampdutyError.getUsrUpd()));
		this.managerDAO.update(documentHStampdutyError);
	}

	@Override
	public TrDocumentHStampdutyError getDocumentHStampdutyErrorByIdDocumentH(Long idDocumentH) {
		if (null == idDocumentH) {
			return null;
		}
		
		Object[][] queryParams = { { TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH} };
		
		return this.managerDAO.selectOne(
				"from TrDocumentHStampdutyError dhse "
				+" join fetch dhse.trDocumentH dh "
				+" left join fetch dhse.trDocumentD dd "
				+" where dh.idDocumentH = :idDocumentH ", 
				queryParams);
	}

	@Override
	public TrDocumentD getLatestUnsignedDocument(AmMsuser user, MsTenant tenant, MsVendor vendor) {
		
		Object[][] params = new Object[][] {
			{AmMsuser.ID_MS_USER_HBM, user.getIdMsUser()},
			{MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant()},
			{MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor()}
		};

		StringBuilder query = new StringBuilder();
		query
			.append("select dds.id_document_d ")
			.append("from tr_document_d_sign dds ")
			.append("join tr_document_d dd on dds.id_document_d = dd.id_document_d ")
			.append("join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
			.append("where dh.is_active = '1' ")
			.append("and dds.sign_date is null ")
			.append("and dds.id_ms_user = :idMsUser ")
			.append("and dd.id_ms_tenant = :idMsTenant ")
			.append("and dd.id_ms_vendor = :idMsVendor ")
			.append("order by dds.id_document_d_sign desc limit 1");
		
		BigInteger idDocumentD = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentD) {
			return null;
		}
		
		return this.getDocumentDetailById(idDocumentD.longValue());
	}

	@Override
	public TrDocumentH getEarliestActiveAgreement(AmMsuser user) {
		StringBuilder query = new StringBuilder();
		query.append(" select dh.id_document_h ")
			 .append(" from tr_document_h dh ")
			 .append(" join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			 .append(" join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			 .append(" where dds.id_ms_user = :idMsUser and dh.is_active = '1' ")
			 .append(" order by dh.dtm_crt asc ")
			 .append(" limit 1 ");
		
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		BigInteger id = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == id) {
			return null;
		}
		return this.managerDAO.selectOne(TrDocumentH.class, id.longValue());
	}

	@Override
	public Integer countCheckDocumentSendStatus(String documentId) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOCUMENT_ID_HBM, documentId);
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT COUNT(*)FROM tr_document_d ")
		     .append(" WHERE CASE ")
		           .append(" WHEN send_status != 3 OR send_status IS NULL THEN TRUE ")
		           .append(" ELSE FALSE ")
		     .append(" END ")
		     .append(" AND id_document_h IN (SELECT id_document_h ")
		                           .append(" FROM tr_document_d ")
		                           .append(" WHERE document_id = :documentId ) ");
		
        BigInteger totaldoc= (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		return totaldoc.intValue();
	}

	@Override
	public String getDocumentIdByEmail(String signerEmail, String tenantCode) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put("signerEmail", signerEmail);
		params.put(MsTenant.TENANT_CODE_HBM, tenantCode);
		
		query.append(" SELECT document_id AS \"documentId\" FROM tr_document_d_sign tdds ")
		.append(" JOIN am_msuser amu ON amu.id_ms_user = tdds.id_ms_user ")
		.append(" JOIN tr_document_d tdd ON tdd.id_document_d = tdds.id_document_d ")
		.append(" JOIN ms_tenant mt ON mt.id_ms_tenant = tdd.id_ms_tenant ");
		
		if (StringUtils.isNotBlank(signerEmail) && StringUtils.isEmpty(tenantCode)) {
			 query.append(" WHERE amu.login_id = :signerEmail AND tdds.sign_date IS null ORDER BY tdd.dtm_crt DESC LIMIT 1");
		}
		else if(StringUtils.isNotBlank(signerEmail) && StringUtils.isNotBlank(tenantCode)) {
			 query.append(" WHERE amu.login_id = :signerEmail AND tdds.sign_date IS null AND mt.tenant_code = :tenantCode ORDER BY tdd.dtm_crt DESC LIMIT 1 ");
		}
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public String getDocumentIdByPSREDocumentId(String psreDocument){
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put("psreDocument", psreDocument);
		
		query.append(" SELECT document_id AS \"documentId\" FROM tr_document_d");
		
		if (StringUtils.isNotBlank(psreDocument)) {
			 query.append(" WHERE psre_document_id = :psreDocument ");
		}
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<TrDocumentD> getListDocumentStatusPendingByloginId(String loginId) {
        Object[][] queryParams = {{ AmMsuser.LOGIN_ID_HBM, StringUtils.upperCase(loginId) }};
		
		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
			   + "join fetch docD.trDocumentH docH "
			   + "join fetch docH.amMsuserByIdMsuserCustomer mu "
			   + "where docD.sendStatus = 0 "
			   + "and mu.isActive = '1' "
			   + "and mu.loginId = :loginId ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	public MsLov getLovSignStatusByPSREDocumentId(String psreDocument) {
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put("psreDocument", psreDocument);
		
		query.append(" SELECT ml.id_lov AS \"lovSignStatus\" FROM tr_document_d tdd")
			.append(" JOIN ms_lov ml ON ml.id_lov = tdd.lov_sign_status ");
		
		if (StringUtils.isNotBlank(psreDocument)) {
			 query.append(" WHERE tdd.psre_document_id = :psreDocument ");
		}		
		
		BigInteger idLov = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null==idLov) {
			return null;
		}
		
		return this.managerDAO.selectOne(MsLov.class, new Object[][] {{ Restrictions.eq(MsLov.ID_LOV_HBM, idLov.longValue()) }});
	}
	
	private String constructParamsctivationStatusByDocumentId(Map<String, Object> params, String documentId) {
        StringBuilder query = new StringBuilder();
        
        if (StringUtils.isNotBlank(documentId)) {
            query.append(" JOIN am_msuser amu ON amu.id_ms_user = mvru.id_ms_user ")
            .append(" JOIN LATERAL (SELECT role_name, id_ms_tenant FROM am_memberofrole ammr ")
            .append(" JOIN am_msrole amr ON amr.id_ms_role = ammr.id_ms_role ")
            .append(" WHERE ammr.id_ms_user = mvru.id_ms_user ORDER BY ammr.dtm_crt DESC LIMIT 1) amr ON TRUE ")
            .append(" JOIN tr_document_d_sign ds ON ds.id_ms_user = mvru.id_ms_user ")
            .append(" JOIN tr_document_d dd ON dd.id_document_d = ds.id_document_d ")
            .append(" JOIN tr_document_h dh ON dh.id_document_h = dd.id_document_h AND dh.id_ms_tenant = amr.id_ms_tenant WHERE dd.id_document_d IN ")
            .append(" (SELECT id_document_d FROM tr_document_d WHERE id_document_h IN ")
            .append(" (SELECT id_document_h FROM tr_document_d WHERE document_id = :documentId )) ")
            .append(" AND mvru.id_ms_vendor = dd.id_ms_vendor ");

            params.put("documentId", documentId);
        }
        
        return query.toString();
    }
    
    @Override
    public List<ActivationStatusByDocumentId> getActivationStatusByDocumentId(String documentId) {
        Map<String, Object> params = new HashMap<>();

        String paramsQueryActivationStatusByDocumentId = this.constructParamsctivationStatusByDocumentId(params, documentId);
        StringBuilder query = new StringBuilder();
        
        query.append(" SELECT DISTINCT signer_registered_email AS \"email\", ")
        .append(" mvru.is_active AS \"activationStatus\", ")
        .append(" is_registered AS \"registationStatus\", ")
        .append(" full_name AS \"fullName\", ")
        .append(" mvru.id_ms_user AS \"idMsUser\", ")
        .append(" amr.role_name AS \"roleName\", ")
        .append(" mvru.id_ms_vendor AS \"idMsVendorUser\", ")
        .append(" amr.id_ms_tenant AS \"idMsTenantUser\", ")
        .append(" dd.id_ms_vendor AS \"idMsVendorDocument\" ")
        
        .append(" FROM ms_vendor_registered_user mvru ")
        .append(paramsQueryActivationStatusByDocumentId);
        
        return this.managerDAO.selectForListString(ActivationStatusByDocumentId.class, query.toString(), params, null);
    }
        
    @Override
	public List<Map<String, Object>> getSignerBeanByDocumentId(String documentId) {
        Map<String, Object> params = new HashMap<>();
		params.put("documentId", documentId);

        StringBuilder query = new StringBuilder();
		
        query.append(" SELECT  DISTINCT ml.code AS \"signerType\", amu.login_id AS \"email\" ")
        .append(" FROM tr_document_d_sign tdds ")
        .append(" JOIN am_msuser amu ON amu.id_ms_user = tdds.id_ms_user ")
        .append(" JOIN tr_document_d tdd ON tdd.id_document_d = tdds.id_document_d ")
        .append(" LEFT JOIN ms_lov ml ON ml.id_lov = tdds.lov_signer_type ")
        .append(" WHERE tdd.document_id = :documentId ");
        
        
        return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
    
    
    
	@Override
	public String getDocumentTemplateCodeById(String idDocTemplate){
		Map<String, Object> params = new HashMap<>();
		StringBuilder query = new StringBuilder();
		params.put(MsDocTemplate.ID_DOC_TEMPLATE_HBM, idDocTemplate);
		
		query.append(" SELECT doc_template_code AS \"docTemplateCode\" FROM ms_doc_template mdt ")
				.append(" JOIN tr_document_d tdd ON tdd.id_ms_doc_template = mdt.id_doc_template ");
		
		if (StringUtils.isNotBlank(idDocTemplate)) {
			 query.append(" WHERE tdd.document_id = :idDocTemplate ");
		}
		
		return (String) this.managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public TrDocumentD getDocumentByPsreDocumentId(String psreDocumentId) {
		if (StringUtils.isBlank(psreDocumentId)) {
			return null;
		}
		return this.managerDAO.selectOne(
				"from TrDocumentD dd "
				+ "join fetch dd.trDocumentH dh "
				+ "where dd.psreDocumentId = :psreDocumentId ", 
				new Object[][] {{"psreDocumentId", psreDocumentId}});
	}

	@Override
	public BigInteger countDocumentInAgreement(Long idDocumentH) {
		
		if (null == idDocumentH) {
			return null;
		}
		
		Object[][] params = new Object[][] {{ TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH }};
		
		StringBuilder query = new StringBuilder();
		query
			.append("select count(dd.id_document_d) ")
			.append("from tr_document_d dd ")
			.append("where dd.id_document_h = :idDocumentH ");
		
		return (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
	}
	
	@Override
	public TrDocumentD getLatestDocumentDetailBySignerLoginIdAndIdMsVendor(String loginId, Long idMsVendor) {
		Map<String, Object> params = new HashMap<>();
		params.put("loginId", loginId);
		params.put("idVendor", idMsVendor);

		
		StringBuilder query = new StringBuilder();
		query
			.append(" select dd.id_document_d ")
			.append(" from tr_document_d dd ")
			.append(" join tr_document_d_sign dds on dd.id_document_d = dds.id_document_d ")
			.append(" join am_msuser mu on dds.id_ms_user = mu.id_ms_user ")
			.append(" where login_id = :loginId ")
			.append(" and dd.id_ms_vendor = :idVendor ")
			.append(" order by dds.dtm_crt desc ")
			.append(" limit 1 ");
		
		BigInteger idDocumentD = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		if (null == idDocumentD) {
			return null;
		}
		
		return this.managerDAO.selectOne("from TrDocumentD d "
										 + "join fetch d.msTenant "
										 + "join fetch d.msVendor "
										 + "where d.idDocumentD = :idDocumentD", 
								new Object[][] {{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD.longValue()}});
	}
	

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHNoRollBack(long idDocH, Date currDate, String usrUpd, short totalSigned) {
		TrDocumentH documentH = this.managerDAO.selectOne(TrDocumentH.class, idDocH);
		documentH.setDtmUpd(currDate);
		documentH.setUsrUpd(MssTool.maskData(usrUpd));
		documentH.setTotalSigned(totalSigned);
		
		this.managerDAO.update(documentH);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDSignNoRollBack(long idDocDSign, Date currDate, String usrUpd) {
		TrDocumentDSign documentDSign = this.managerDAO.selectOne(TrDocumentDSign.class, idDocDSign);
		documentDSign.setDtmUpd(currDate);
		documentDSign.setSignDate(currDate);
		documentDSign.setUsrUpd(MssTool.maskData(usrUpd));
		this.managerDAO.update(documentDSign);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentH> getListDocumentHeaderByProsesMeteraiNewTran(Short prosesMeterai) {
		Object[][] queryParams = {{"prosesMaterai", prosesMeterai}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "join fetch docH.msTenant mt "
				+ "left join fetch docH.amMsuserByIdMsuserCustomer am "
				+ "join fetch docH.msOffice mo "
				+ "where docH.prosesMaterai = :prosesMaterai and mo.isActive ='1' "
				+ "order by docH.idDocumentH ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentD> getListDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId) {
		Object[][] queryParams = {{TrDocumentH.ID_DOCUMENT_H_HBM, documentHeaderId}};
		
		return (List<TrDocumentD>) this.managerDAO.list(
				"from TrDocumentD docD "
				+ "join fetch docD.trDocumentH docH "
				+ "join fetch docH.msOffice mo "
				+ "join fetch docH.msLov ml "
				+ "join fetch docD.msTenant mt "
				+ "join fetch docD.msVendor mv "
				+ "left join fetch docD.msDocTemplate mdt "
				+ "left join fetch docD.msPeruriDocType mpdt "
				+ "left join fetch docD.msLovIdType mlid "
				+ "where docH.idDocumentH = :idDocumentH ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDetailNewTran(TrDocumentD doc) {
		doc.setUsrUpd(MssTool.maskData(doc.getUsrUpd()));
		this.managerDAO.update(doc);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHNewTran(TrDocumentH documentH) {
		documentH.setUsrUpd(MssTool.maskData(documentH.getUsrUpd()));
		this.managerDAO.update(documentH);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentDetailSdtNewTran(TrDocumentDStampduty documentDSdt) {
		documentDSdt.setUsrUpd(MssTool.maskData(documentDSdt.getUsrUpd()));
		this.managerDAO.update(documentDSdt);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentD getDocumentDetailByDocIdNewTran(String docId) {
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " join fetch dh.msOffice mo"
				+ " join fetch d.msTenant tenant "
				+ " join fetch d.msVendor vendor "
				+ " join fetch d.msLovByLovSignStatus signStatus "
				+ " join fetch d.msLovByLovPaymentSignType signStatus "
				+ " where d.documentId = :documentId ", 
				new Object[][] {{TrDocumentD.DOCUMENT_ID_HBM, StringUtils.upperCase(docId)}});
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentHStampdutyError getDocumentHStampdutyErrorByIdDocumentHNewTran(Long idDocumentH) {
		if (null == idDocumentH) {
			return null;
		}
		
		Object[][] queryParams = { { TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH} };
		
		return this.managerDAO.selectOne(
				"from TrDocumentHStampdutyError dhse "
				+" join fetch dhse.trDocumentH dh "
				+" left join fetch dhse.trDocumentD dd "
				+" where dh.idDocumentH = :idDocumentH ", 
				queryParams);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError documentHEmeteraiError) {
		documentHEmeteraiError.setUsrCrt(MssTool.maskData(documentHEmeteraiError.getUsrCrt()));
		this.managerDAO.insert(documentHEmeteraiError);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateDocumentHStampdutyErrorNewTran(TrDocumentHStampdutyError documentHEmeteraiError) {
		documentHEmeteraiError.setUsrUpd(MssTool.maskData(documentHEmeteraiError.getUsrUpd()));
		this.managerDAO.update(documentHEmeteraiError);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public TrDocumentHStampdutyError getDocumentHStampdutyErrorNewTran(Long idDocumentH, Long idDocumentD) {
		
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);
		if (null != idDocumentD) {
			params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		}
		
		StringBuilder query = new StringBuilder()
				.append("select id_document_h_stampduty_error ")
				.append("from tr_document_h_stampduty_error ")
				.append("where id_document_h = :idDocumentH ")
				.append((null != idDocumentD) ? "and id_document_d = :idDocumentD " : "and id_document_d is null ");
		
		BigInteger idDocumentHStampduty = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentHStampduty) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrDocumentHStampdutyError dhse "
				+" join fetch dhse.trDocumentH dh "
				+" join fetch dh.msTenant mt "
				+" left join fetch dhse.trDocumentD dd "
				+" where dhse.idDocumentHStampdutyError = :idDocumentHStampdutyError ",
				new Object[][] {{ TrDocumentHStampdutyError.ID_DOCUMENT_H_STAMPDUTY_ERROR_HBM, idDocumentHStampduty.longValue() }});
	}
	
	@Override
	public List<TrDocumentHStampdutyError> getListDocumentHStampdutyErrorByIdDocumentH(Long idDocumentH) {
		if (null == idDocumentH) {
			return null;
		}
		
		Object[][] queryParams = { { TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH} };
		
		return (List<TrDocumentHStampdutyError>) this.managerDAO.list(
				"from TrDocumentHStampdutyError dhse "
				+" join fetch dhse.trDocumentH dh "
				+" left join fetch dhse.trDocumentD dd "
				+" where dh.idDocumentH = :idDocumentH ", 
				queryParams).get(AmGlobalKey.MAP_RESULT_LIST);
	}
	
	private String constructParamMonitoring(Map<String, Object> params, String nomorDokumen,
			String tipeDokumen, Date tanggalDokumenMulai, Date tanggalDokumenSampai, 
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode, String taxType, String cabang) {
		StringBuilder query = new StringBuilder();
		
		if (StringUtils.isNotBlank(nomorDokumen)) {
			query.append(" and dh.ref_number LIKE :nomorDokumen ");
			params.put("nomorDokumen", "%" + nomorDokumen + "%");
		}
		
		if (StringUtils.isNotBlank(tipeDokumen)) {
			query.append(" and ml.code LIKE :tipeDokumen ");
			params.put("tipeDokumen", "%" + tipeDokumen + "%");
		}
		
		if(tanggalDokumenMulai != null && tanggalDokumenSampai != null) {			
			query.append("and dd.request_date >= :tanggalDokumenMulai and dd.request_date <= :tanggalDokumenSampai ");
			params.put("tanggalDokumenMulai", tanggalDokumenMulai);
			params.put("tanggalDokumenSampai", tanggalDokumenSampai);
		} else {
			 	 query.append("and dd.request_date >= date_trunc('MONTH', now()) and dd.request_date <= now() ");
			 
		}
		
		if (StringUtils.isNotBlank(hasilStamping)) {
			if(hasilStamping.equals(GlobalVal.HASIL_STAMPING_NOT_STARTED)) {
			  query.append(" and dh.proses_materai = 0 ");
			} 
			else if(hasilStamping.equals(GlobalVal.HASIL_STAMPING_FAILED)) {
			  query.append(" and dh.proses_materai in (1,51,321,521,61,71)");
		    } 
			else if(hasilStamping.equals(GlobalVal.HASIL_STAMPING_SUCCESS)) {
			  query.append(" and dh.proses_materai in (3,53,323,523,63,73)");
		    } 
			else if(hasilStamping.equals(GlobalVal.HASIL_STAMPING_IN_PROGRESS)) {
			  query.append(" and dh.proses_materai in (2,52,322,522,5,55,325,525,62,64,65,72,74,75,54)");
		    } 
		}
		
		if (StringUtils.isNotBlank(jenisDokumen)) {
			query.append(" and pdt.doc_name LIKE :jenisDokumen ");
			params.put("jenisDokumen", "%" + jenisDokumen + "%");
		}
		
		if (StringUtils.isNotBlank(templateDokumen)) {
			if(templateDokumen.equals("MANUAL")) {
			  query.append(" and mdt.doc_template_name is null ");
			} else {
			  query.append(" and mdt.doc_template_name LIKE :templateDokumen ");
			}
			params.put("templateDokumen", "%" + StringUtils.upperCase(templateDokumen) + "%");
		}
		
		if (StringUtils.isNotBlank(noSN)) {
			query.append(" and bm.stamp_duty_no LIKE :noSN ");
			params.put("noSN", "%" + StringUtils.upperCase(noSN) + "%");
		}
		
		if (StringUtils.isNotBlank(tenantCode)) {
			query.append(" and mt.tenant_code = :tenantCode ");
			params.put(MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode));
		}
		
		if (StringUtils.isNotBlank(taxType)) {
			if(taxType.equals("Pemungut")) {
			   query.append(" and dh.is_postpaid_stampduty = '1' ");
			}
			else if(taxType.equals("Non Pemungut")) {
			   query.append(" and dh.is_postpaid_stampduty = '0' ");
			}
		}
		
		if (StringUtils.isNotBlank(cabang)) {
			query.append(" and mo.office_code = :cabang ");
			params.put("cabang", StringUtils.upperCase(cabang));
		}
		
		return query.toString();
	}

	@Override
	public List<Map<String, Object>> getListMonitoringEmbed(int min, int max, String nomorDokumen, String tipeDokumen, String hasilStamping, 
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, Date tanggalDokumenMulai, Date tanggalDokumenSampai) {

		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, "", "");
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" dd.id_document_d AS \"IdDokumenD\", ")
		     .append(" dd.id_document_h AS \"IdDokumenH\", ")
		     .append(" TO_CHAR(dd.request_date, 'dd Mon yyyy HH24:MI') AS \"tanggalDokumen\", ")
		     .append(" dd.document_name AS \"namaDokumen\", ")
		     .append(" pdt.doc_name AS \"jenisDokumen\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" CAST(dd.document_nominal AS varchar) AS \"nominalDokumen\", ")
		     .append(" COALESCE(mdt.doc_template_name, 'MANUAL') AS \"templateDokumen\", ")
		        .append(" CASE WHEN dh.proses_materai = 0 THEN 'Not Started' ")
                .append(" WHEN dh.proses_materai in (1,51,321,521) THEN 'Failed' ")
                .append(" WHEN dh.proses_materai in (3,53,323,523) THEN 'Success' ")
                .append(" WHEN dh.proses_materai in (2,52,322,522,5,55,325,525,54) THEN 'In Progress' ")
		        .append(" ELSE 'Unknown' ")
		        .append(" END AS \"hasilStamping\", ")
		     .append(" bm.stamp_duty_no AS \"noSN\", ")
		     .append(" concat(COALESCE(dd.total_stamping, '0'), '/', COALESCE(dd.total_materai, '0'))AS \"prosesMaterai\", ")
		     .append(" dd.id_no AS \"noIdentitas\", ")
		     .append(" dd.id_name AS \"namaIdentitas\", ")
		     .append(" row_number() over (order by dd.id_document_d) AS \"rowNum\" ")
		     .append(" from tr_document_d dd ") 
		     .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
		     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
		     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
		     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
		     .append(" left join lateral ( ")
		            .append(" Select mdt.doc_template_name from ms_doc_template mdt ")
		            .append(" where dd.id_ms_doc_template = mdt.id_doc_template ")
		     .append(" ) mdt ON TRUE ")
		     .append(" left join lateral ( ")
		            .append(" Select bm.notes, st.stamp_duty_no, bm.trx_date ")
	                .append(" from tr_balance_mutation bm ")
	                .append(" join tr_stamp_duty st on st.id_stamp_duty = bm.id_stamp_duty ")
	                .append(" join ms_lov lovBalType on lovBalType.id_lov = bm.lov_balance_type ")
	                .append(" where lovBalType.code = 'SDT_POSTPAID' ") //butuh join dgn lov type buat ambil lov balance type karena hanya ambil data balmut yang ada stamp duty nya
	                .append(" and bm.id_document_d = dd.id_document_d ")
	         .append(" ) bm ON TRUE ")
		     .append(" where 1 = 1 ")
		     .append(" and dh.is_manual_upload = '1' ")
		     .append(paramQueryMonitoring)
		     .append(" order by dd.request_date, dh.ref_number ")
		     .append(" ) ")
		     //jangan pake dtm_crt karena ga ada index. Check ada existing index berhubungan dengan tanggal yg bisa digunakan tidak
		     // ada reqdate index yang valuenya sama dgn dtm_crt untuk case ini
		     
		     .append(" select \"nomorDokumen\", \"tanggalDokumen\", \"namaDokumen\", \"jenisDokumen\", \"tipeDokumen\", \"nominalDokumen\", ")
		     .append(" \"templateDokumen\", \"hasilStamping\", \"noSN\", \"prosesMaterai\",\"noIdentitas\", \"namaIdentitas\", \"IdDokumenD\", \"IdDokumenH\" ")
		     .append(" from ListMonitoring ")
		     .append(" WHERE \"rowNum\" between :min and :max  ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public Integer countListMonitoringEmbed(int min, int max, String nomorDokumen, String tipeDokumen, String hasilStamping, 
			String jenisDokumen, String templateDokumen, String noSN, String tenantCode, Date tanggalDokumenMulai, Date tanggalDokumenSampai) {
		Map<String, Object> params = new HashMap<>();

		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, "","");
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" dd.id_document_d AS \"IdDokumenD\", ")
		     .append(" dd.id_document_h AS \"IdDokumenH\", ")
		     .append(" TO_CHAR(dd.request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS \"tanggalDokumen\", ")
		     .append(" dd.document_name  AS \"namaDokumen\", ")
		     .append(" pdt.doc_name AS \"jenisDokumen\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" dd.document_nominal AS \"nominalDokumen\", ")
		     .append(" COALESCE(mdt.doc_template_name, 'MANUAL') AS \"templateDokumen\", ")
		           .append(" CASE WHEN dh.proses_materai = 0 THEN 'Not Started' ")
                   .append(" WHEN dh.proses_materai in (1,51,321,521) THEN 'Failed' ")
                   .append(" WHEN dh.proses_materai in (3,53,323,523) THEN 'Success' ")
                   .append(" WHEN dh.proses_materai in (2,52,322,522,5,55,325,525) THEN 'In Progress' ")
	               .append(" ELSE 'Unknown' ")
	               .append(" END AS \"hasilStamping\", ")
	         .append(" bm.stamp_duty_no AS \"noSN\", ")
	         .append(" concat(COALESCE(dd.total_stamping, '0'), '/', COALESCE(dd.total_materai, '0'))AS \"prosesMaterai\", ")
	         .append(" dd.id_no AS \"noIdentitas\", ")
	         .append(" dd.id_name AS \"namaIdentitas\", ")
	         .append(" row_number() over (order by dd.id_document_d) AS \"rowNum\" ")
	         .append(" from tr_document_d dd ") 
	         .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
    	     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
    	     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
     	     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
	         .append(" left join lateral ( ")
	               .append(" Select mdt.doc_template_name from ms_doc_template mdt ")
	               .append(" where dd.id_ms_doc_template = mdt.id_doc_template ")
	         .append(" ) mdt ON TRUE ")
	         .append(" left join lateral ( ")
	               .append(" Select bm.notes, st.stamp_duty_no, bm.trx_date ")
                   .append(" from tr_balance_mutation bm ")
                   .append(" join tr_stamp_duty st on st.id_stamp_duty = bm.id_stamp_duty ")
                   .append(" join ms_lov lovBalType on lovBalType.id_lov = bm.lov_balance_type ")
                   .append(" where lovBalType.code = 'SDT_POSTPAID' ")
                   .append(" and bm.id_document_d = dd.id_document_d ")
             .append(" ) bm ON TRUE ")
	         .append(" where 1 = 1 ")
	         .append(" and dh.is_manual_upload = '1' ")
	         .append(paramQueryMonitoring)
	         .append(" order by dd.request_date, dh.ref_number ")
	         .append(" ) ")
		     .append(" select count(*) ")
		     .append(" from ListMonitoring ");
		
        BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		return totalData.intValue();
	}
	
	@Override
	public List<TaxReportBean> getListMonitoringReportEmbed(String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode ,Date tanggalDokumenMulai,
			Date tanggalDokumenSampai) {
		Map<String, Object> params = new HashMap<>();

		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, "", "");
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" pdt.doc_code AS \"jenisDokumen\", ")
		     .append(" dd.document_nominal AS \"nominalDokumen\", ")
		     .append(" TO_CHAR(dd.request_date, 'YYYY-MM-DD') \"tanggalDokumen\", ")
		     .append(" mli.code AS \"identitas\", ")
		     .append(" dd.id_no AS \"noIdentitas\", ")
		     .append(" dd.id_name AS \"namaIdentitas\", ")
		     .append(" cast (stamp_duty_total_fee AS int) AS \"stampDutyFee\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" bm.stamp_duty_no AS \"noSN\" ")
		     .append(" from tr_document_d dd ")
		     .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
		     .append(" join ms_lov mli on dd.lov_id_type = mli.id_lov  ")
		     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
    	     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
		     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
		     .append("  left join lateral ( ")
		     .append(" Select mdt.doc_template_name from ms_doc_template mdt where dd.id_ms_doc_template = mdt.id_doc_template ")
		     .append(" ) mdt ON TRUE ")
		         .append(" left join lateral ( ")
		            .append(" Select sum(stamp_duty_fee) as stamp_duty_total_fee ")
		            .append(" from tr_balance_mutation bmd ")
		            .append(" join tr_stamp_duty st on st.id_stamp_duty = bmd.id_stamp_duty ")
		            .append(" join ms_lov lovBalType on lovBalType.id_lov = bmd.lov_balance_type ")
		            .append(" where lovBalType.code = 'SDT_POSTPAID'  and bmd.id_document_d = dd.id_document_d ")
		         .append(" ) bmd ON TRUE ")
		         .append(" left join lateral ( ")
		           .append(" Select bmd.notes, st.stamp_duty_no, bmd.trx_date ")
		           .append(" from tr_balance_mutation bmd ")
		           .append(" join tr_stamp_duty st on st.id_stamp_duty = bmd.id_stamp_duty ")
		           .append(" join ms_lov lovBalType on lovBalType.id_lov = bmd.lov_balance_type ")
		           .append(" where lovBalType.code = 'SDT_POSTPAID' and bmd.id_document_d = dd.id_document_d ")
		         .append(" ) bm ON TRUE ")
		     .append(" where dh.proses_materai in (3,53,323,523) and dh.is_manual_upload = '1' ")
		     .append(" and stamp_duty_total_fee IS NOT NULL ")
		     .append(paramQueryMonitoring)
		     .append(" ) ")
		     .append(" select distinct \"nomorDokumen\", \"jenisDokumen\", \"nominalDokumen\", \"tanggalDokumen\", \"identitas\", \"noIdentitas\", \"namaIdentitas\", \"stampDutyFee\" ")
		     .append(" from ListMonitoring ");
		
		return this.managerDAO.selectForListString(TaxReportBean.class, query.toString(), params, null);
	}
	
	@Override
	public List<Map<String, Object>> getListMonitoring(int min, int max, String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode,
			String taxType, String cabang, Date tanggalDokumenMulai, Date tanggalDokumenSampai) {

		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, taxType, cabang);
		
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" dd.id_document_d AS \"IdDokumenD\", ")
		     .append(" dd.id_document_h AS \"IdDokumenH\", ")
		     .append(" TO_CHAR(dd.request_date, 'dd Mon yyyy HH24:MI') AS \"tanggalDokumen\", ")
		     .append(" dd.document_name AS \"namaDokumen\", ")
		     .append(" pdt.doc_name AS \"jenisDokumen\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" CAST(dd.document_nominal AS varchar) AS \"nominalDokumen\", ")
		     .append(" COALESCE(mdt.doc_template_name, 'MANUAL') AS \"templateDokumen\", ")
		         .append(" CASE WHEN dh.proses_materai = 0 THEN 'Not Started' ")
                 .append(" WHEN dh.proses_materai in (1,51,321,521,61,71) THEN 'Failed' ")
                 .append(" WHEN dh.proses_materai in (3,53,323,523,63,73) THEN 'Success' ")
                 .append(" WHEN dh.proses_materai in (2,52,322,522,5,55,325,525,62,64,65,72,74,75,54) THEN 'In Progress' ")
                 .append(" ELSE 'Unknown' ")
                 .append(" END AS \"hasilStamping\", ")
		     .append(" bm.stamp_duty_no AS \"noSN\", ")
		     .append(" concat(COALESCE(dd.total_stamping, '0'), '/', COALESCE(dd.total_materai, '0'))AS \"prosesMaterai\", ")
		         .append(" CASE WHEN dh.is_postpaid_stampduty = '0' THEN 'Non Pemungut' ")
		              .append(" WHEN dh.is_postpaid_stampduty = '1' THEN 'Pemungut' ")
		         .append(" ELSE 'Unknown'  END AS \"taxType\", ")
		     .append(" dd.id_no AS \"noIdentitas\", ")
		     .append(" dd.id_name AS \"namaIdentitas\", ")
		     .append(" mo.office_name AS \"cabang\", ")
		     .append(" dd.archive_document_status AS \"archiveDocumentStatus\", ")
		     .append(" row_number() over (order by dd.id_document_d) AS \"rowNum\" ")
		     .append(" from tr_document_d dd ") 
		     .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
		     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
		     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
		     .append(" join ms_office mo on mo.id_ms_office = dh.id_ms_office ")
		     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
		     .append(" left join lateral ( ")
		            .append(" Select mdt.doc_template_name from ms_doc_template mdt ")
		            .append(" where dd.id_ms_doc_template = mdt.id_doc_template ")
		     .append(" ) mdt ON TRUE ")
		     .append(" left join lateral ( ")
		            .append(" Select bm.notes, st.stamp_duty_no, bm.trx_date ")
	                .append(" from tr_balance_mutation bm ")
	                .append(" join tr_stamp_duty st on st.id_stamp_duty = bm.id_stamp_duty ")
	                .append(" join ms_lov lovBalType on lovBalType.id_lov = bm.lov_balance_type ")
	                .append(" where lovBalType.code IN ('SDT', 'SDT_POSTPAID')  ") //butuh join dgn lov type buat ambil lov balance type karena hanya ambil data balmut yang ada stamp duty nya
	                .append(" and bm.id_document_d = dd.id_document_d ")
	         .append(" ) bm ON TRUE ")
		     .append(" where 1 = 1 ")
		     .append(" and dh.is_manual_upload = '1' ")
		     .append(paramQueryMonitoring)
		     .append(" order by dd.request_date, dh.ref_number ")
		     .append(" ) ")
		     //jangan pake dtm_crt karena ga ada index. Check ada existing index berhubungan dengan tanggal yg bisa digunakan tidak
		     // ada reqdate index yang valuenya sama dgn dtm_crt untuk case ini
		     
		     .append(" select \"nomorDokumen\", \"tanggalDokumen\", \"namaDokumen\", \"jenisDokumen\", \"tipeDokumen\", \"nominalDokumen\", ")
		     .append(" \"templateDokumen\", \"hasilStamping\", \"noSN\", \"prosesMaterai\",\"noIdentitas\", \"namaIdentitas\", \"taxType\", \"cabang\", \"IdDokumenD\", \"IdDokumenH\", \"archiveDocumentStatus\" ")
		     .append(" from ListMonitoring ")
		     .append(" WHERE \"rowNum\" between :min and :max  ");
		
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}

	@Override
	public Integer countListMonitoring(int min, int max, String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode,
			String taxType, String cabang, Date tanggalDokumenMulai, Date tanggalDokumenSampai) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		
		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, taxType, cabang);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" dd.id_document_d AS \"IdDokumenD\", ")
		     .append(" dd.id_document_h AS \"IdDokumenH\", ")
		     .append(" TO_CHAR(dd.request_date, 'dd Mon yyyy HH24:MI') AS \"tanggalDokumen\", ")
		     .append(" dd.document_name AS \"namaDokumen\", ")
		     .append(" pdt.doc_name AS \"jenisDokumen\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" CAST(dd.document_nominal AS varchar) AS \"nominalDokumen\", ")
		     .append(" COALESCE(mdt.doc_template_name, 'MANUAL') AS \"templateDokumen\", ")
		          .append(" CASE WHEN dh.proses_materai = 0 THEN 'Not Started' ")
                  .append(" WHEN dh.proses_materai in (1,51,321,521,61,71) THEN 'Failed' ")
                  .append(" WHEN dh.proses_materai in (3,53,323,523,63,73) THEN 'Success' ")
                  .append(" WHEN dh.proses_materai in (2,52,322,522,5,55,325,525,62,64,65,72,74,75) THEN 'In Progress' ")
                  .append(" ELSE 'Unknown' ")
		          .append(" END AS \"hasilStamping\", ")
		     .append(" bm.stamp_duty_no AS \"noSN\", ")
		     .append(" concat(COALESCE(dd.total_stamping, '0'), '/', COALESCE(dd.total_materai, '0'))AS \"prosesMaterai\", ")
		         .append(" CASE WHEN dh.is_postpaid_stampduty = '0' THEN 'Non Pemungut' ")
		              .append(" WHEN dh.is_postpaid_stampduty = '1' THEN 'Pemungut' ")
		         .append(" ELSE 'Unknown'  END AS \"taxType\", ")
		     .append(" dd.id_no AS \"noIdentitas\", ")
		     .append(" dd.id_name AS \"namaIdentitas\", ")
		     .append(" mo.office_name AS \"cabang\", ")
		     .append(" dd.archive_document_status AS \"archiveDocumentStatus\", ")
		     .append(" row_number() over (order by dd.id_document_d) AS \"rowNum\" ")
		     .append(" from tr_document_d dd ") 
		     .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
		     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
		     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
		     .append(" join ms_office mo on mo.id_ms_office = dh.id_ms_office ")
		     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
		     .append(" left join lateral ( ")
		            .append(" Select mdt.doc_template_name from ms_doc_template mdt ")
		            .append(" where dd.id_ms_doc_template = mdt.id_doc_template ")
		     .append(" ) mdt ON TRUE ")
		     .append(" left join lateral ( ")
		            .append(" Select bm.notes, st.stamp_duty_no, bm.trx_date ")
	                .append(" from tr_balance_mutation bm ")
	                .append(" join tr_stamp_duty st on st.id_stamp_duty = bm.id_stamp_duty ")
	                .append(" join ms_lov lovBalType on lovBalType.id_lov = bm.lov_balance_type ")
	                .append(" where lovBalType.code IN ('SDT', 'SDT_POSTPAID') ") //butuh join dgn lov type buat ambil lov balance type karena hanya ambil data balmut yang ada stamp duty nya
	                .append(" and bm.id_document_d = dd.id_document_d ")
	         .append(" ) bm ON TRUE ")
		     .append(" where 1 = 1 ")
		     .append(" and dh.is_manual_upload = '1' ")
		     .append(paramQueryMonitoring)
		     .append(" order by dd.request_date, dh.ref_number ")
		     .append(" ) ")
		     .append(" select count(*) ")
		     .append(" from ListMonitoring ");
		
        BigInteger totalData = (BigInteger) this.managerDAO.selectOneNativeString(query.toString(), params);
		
		return totalData.intValue();
	}

	@Override
	public List<TaxReportBean> getListMonitoringReport(String nomorDokumen, String tipeDokumen,
			String hasilStamping, String jenisDokumen, String templateDokumen, String noSN, String tenantCode,
			String taxType, String cabang, Date tanggalDokumenMulai, Date tanggalDokumenSampai) {
		
		Map<String, Object> params = new HashMap<>();
		
		String paramQueryMonitoring = this.constructParamMonitoring(params, nomorDokumen, tipeDokumen,tanggalDokumenMulai, 
				tanggalDokumenSampai, hasilStamping, jenisDokumen, templateDokumen, noSN, tenantCode, taxType, cabang);
		
		StringBuilder query = new StringBuilder();
		query.append(" with ListMonitoring AS ( ")
		     .append(" select dh.ref_number AS \"nomorDokumen\", ")
		     .append(" dd.id_document_d AS \"IdDokumenD\", ")
		     .append(" dd.id_document_h AS \"IdDokumenH\", ")
		     .append(" TO_CHAR(dd.request_date, 'YYYY-MM-DD') AS \"tanggalDokumen\", ")
		     .append(" dd.document_name AS \"namaDokumen\", ")
		     .append(" pdt.doc_name AS \"jenisDokumen\", ")
		     .append(" ml.description AS \"tipeDokumen\", ")
		     .append(" dd.document_nominal AS \"nominalDokumen\", ")
		     .append(" cast (stamp_duty_total_fee AS int) AS \"stampDutyFee\", ")
		     .append(" COALESCE(mdt.doc_template_name, 'MANUAL') AS \"templateDokumen\", ")
		          .append(" CASE WHEN dh.proses_materai = 0 THEN 'Not Started' ")
                  .append(" WHEN dh.proses_materai in (1,51,321,521) THEN 'Failed' ")
                  .append(" WHEN dh.proses_materai in (3,53,323,523) THEN 'Success' ")
                  .append(" WHEN dh.proses_materai in (2,52,322,522,5,55,325,525) THEN 'In Progress' ")  
                  .append(" ELSE 'Unknown' ")
	              .append(" END AS \"hasilStamping\", ")
		     .append(" concat(COALESCE(dd.total_stamping, '0'), '/', COALESCE(dd.total_materai, '0'))AS \"prosesMaterai\", ")
		         .append(" CASE WHEN dh.is_postpaid_stampduty = '0' THEN 'Non Pemungut' ")
		              .append(" WHEN dh.is_postpaid_stampduty = '1' THEN 'Pemungut' ")
		         .append(" ELSE 'Unknown'  END AS \"taxType\", ")
		     .append(" mli.code AS \"identitas\", ")
		     .append(" dd.id_no AS \"noIdentitas\", ")
		     .append(" dd.id_name AS \"namaIdentitas\", ")
		     .append(" mo.office_name AS \"cabang\", ")
		     .append(" row_number() over (order by dd.id_document_d) AS \"rowNum\" ")
		     .append(" from tr_document_d dd ") 
		     .append(" join tr_document_h dh on dd.id_document_h = dh.id_document_h ")
		     .append(" join ms_lov ml on dh.lov_doc_type = ml.id_lov ")
		     .append(" join ms_lov mli on dd.lov_id_type = mli.id_lov  ")
		     .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
		     .append(" join ms_office mo on mo.id_ms_office = dh.id_ms_office ")
		     .append(" join ms_peruri_doc_type pdt on dd.id_peruri_doc_type = pdt.id_peruri_doc_type ")
		     .append(" left join lateral ( ")
		            .append(" Select mdt.doc_template_name from ms_doc_template mdt ")
		            .append(" where dd.id_ms_doc_template = mdt.id_doc_template ")
		     .append(" ) mdt ON TRUE ")
		     .append(" left join lateral ( ")
		            .append(" Select sum(stamp_duty_fee) as stamp_duty_total_fee ")
	                .append(" from tr_balance_mutation bm ")
	                .append(" join tr_stamp_duty st on st.id_stamp_duty = bm.id_stamp_duty ")
	                .append(" join ms_lov lovBalType on lovBalType.id_lov = bm.lov_balance_type ")
	                .append(" where lovBalType.code = 'SDT_POSTPAID' ") //butuh join dgn lov type buat ambil lov balance type karena hanya ambil data balmut yang ada stamp duty nya
	                .append(" and bm.id_document_d = dd.id_document_d ")
	         .append(" ) bm ON TRUE ")
		     .append(" where dh.proses_materai in (3,53,323,523) ")
		     .append(" and dh.is_manual_upload = '1' ")
		     .append(" and stamp_duty_total_fee IS NOT NULL ")
		     .append(paramQueryMonitoring)
		     .append(" order by dd.request_date, dh.ref_number ")
		     .append(" ) ")
		     //jangan pake dtm_crt karena ga ada index. Check ada existing index berhubungan dengan tanggal yg bisa digunakan tidak
		     // ada reqdate index yang valuenya sama dgn dtm_crt untuk case ini
		     
		     .append(" select distinct \"nomorDokumen\", \"jenisDokumen\", \"nominalDokumen\", \"tanggalDokumen\", \"identitas\", \"noIdentitas\", \"namaIdentitas\", \"stampDutyFee\" ")
		     .append(" from ListMonitoring ");	
		
		return this.managerDAO.selectForListString(TaxReportBean.class, query.toString(), params, null);
	}
	
	@Override
	public List<DocumentTemplateByTenantBean> getListDocumentTemplateByTenant(long idTenant) {
        Map<String, Object> params = new HashMap<>();
        StringBuilder query = new StringBuilder();
        
        if ((Long)idTenant != null) {
        	query.append(" SELECT doc_template_code AS \"documentTemplateCode\", ")
        	.append(" doc_template_name AS \"documentTemplateName\", ")
        	.append(" doc_template_description AS \"documentTemplateDescription\" ")
        	.append(" FROM ms_doc_template ")
        	.append(" WHERE id_doc_template IN ( ")
        	.append(" SELECT DISTINCT id_doc_template FROM ms_doc_template_sign_loc WHERE id_doc_template IN ( ")
        	.append(" SELECT id_doc_template FROM ms_doc_template WHERE is_active = '1' AND id_ms_tenant = :idTenant AND id_doc_template IN ( ")
        	.append(" SELECT id_doc_template FROM ms_doc_template_sign_loc WHERE lov_sign_type IN ( ")
        	.append(" SELECT lov_sign_type FROM ms_doc_template_sign_loc WHERE lov_sign_type =  ( ")
        	.append(" SELECT id_lov FROM ms_lov WHERE code = 'SDT' AND lov_group = 'SIGN_TYPE'))))) ");

        	params.put("idTenant", idTenant);

        }
        
        return this.managerDAO.selectForListString(DocumentTemplateByTenantBean.class, query.toString(), params, null);
	}

	@Override
	public List<DocumentTemplateSignLocationBean> getListSignLocationByTemplateCodeV2(String documentTemplateCode, long idTenant) {
        Map<String, Object> params = new HashMap<>();
        StringBuilder query = new StringBuilder();
        if(!documentTemplateCode.isEmpty()) {
        	query.append("SELECT sign_location AS \"signLocation\", ")
        	.append(" sign_page AS \"signPage\", ")
        	.append(" seq_no AS \"seqNo\", ")
        	.append(" transform AS \"transform\" ")
        	.append(" FROM ms_doc_template_sign_loc ")
        	.append(" WHERE id_doc_template = ")
        	.append(" (SELECT id_doc_template FROM ms_doc_template WHERE doc_template_code = :documentTemplateCode AND id_ms_tenant = :idTenant) ")
        	.append(" AND lov_sign_type = (SELECT id_lov FROM ms_lov WHERE code = 'SDT' AND lov_group = 'SIGN_TYPE') ");

        	params.put("documentTemplateCode", documentTemplateCode);
        	params.put("idTenant", idTenant);

        }
        return this.managerDAO.selectForListString(DocumentTemplateSignLocationBean.class, query.toString(), params, null);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentDStampduty> getDocumentStampDutyByIdDocumentDNewTran(Long idDocumentD) {
		Object[][] queryParams = {{TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD}};
		
		return (List<TrDocumentDStampduty>) this.managerDAO.list(
				"from TrDocumentDStampduty docSdt "
				+ "join fetch docSdt.trDocumentD docD "
				+ "join fetch docD.trDocumentH docH "
				+ "left join fetch docSdt.trStampDuty sdt "
				+ "left join fetch sdt.msLov sdtStatus "
				+ "where docD.idDocumentD = :idDocumentD "
				+ "order by docSdt.signPage, docSdt.seqNo ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<TrDocumentHStampdutyError> getDocumentHStampdutyErrorsByIdDocumentH(Long idDocumentH) {
		Object[][] params = {{ TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH }};
		return (List<TrDocumentHStampdutyError>) managerDAO.list(
				"from TrDocumentHStampdutyError hse "
				+ "join fetch hse.trDocumentH dh "
				+ "where dh.idDocumentH = :idDocumentH ", params).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<MsDocTemplateSignLoc> getListSignLocation(String documentTemplateCode, String signTypeCode, String tenantCode) {
		Object[][] queryParams = {
				{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)},
				{MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_SIGN_TYPE},
				{MsLov.CODE_HBM, StringUtils.upperCase(signTypeCode)},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return (List<MsDocTemplateSignLoc>) this.managerDAO.list(
				" from MsDocTemplateSignLoc dtsl " 
				+ " join fetch dtsl.msDocTemplate dt "
				+ " join fetch dt.msTenant mt "
				+ " left join fetch dtsl.msLovByLovSignerType mlSigner  "
				+ " join fetch dtsl.msLovByLovSignType mlSign "
				+ " where dt.docTemplateCode = :docTemplateCode "
				+ " and mlSign.lovGroup = :lovGroup and mlSign.code = :code "
				+ " and mt.tenantCode = :tenantCode "
				+ " order by dtsl.signPage, dtsl.seqNo ",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public TrDocumentD getDocumentDetailByTenantTrxId(String trxId) {
		return managerDAO.selectOne(TrDocumentD.class, new Object[][] {{Restrictions.eq(TrDocumentD.TENANT_TRANSACTION_ID_HBM, trxId)}});
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentDetailNewTrx(TrDocumentD doc) {
		doc.setUsrCrt(MssTool.maskData(doc.getUsrCrt()));
		managerDAO.insert(doc);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentDetailSdtNewTrx(TrDocumentDStampduty documentDSdt) {
		documentDSdt.setUsrCrt(MssTool.maskData(documentDSdt.getUsrCrt()));
		this.managerDAO.insert(documentDSdt);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentH> getListDocumentHeaderByCallbackProcessNewTrx(Short callbackProcess) {
		Object[][] queryParams = {{TrDocumentH.CALLBACK_PROCESS_HBM, callbackProcess}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "left join fetch docH.amMsuserByIdMsuserCustomer am "
				+ "join fetch docH.msOffice mo "
				+ "join fetch docH.msTenant mt "
				+ "left join fetch docH.msBusinessLine bl "
				+ "where docH.callbackProcess = :callbackProcess and mo.isActive ='1' ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentH> getListDocumentHeaderByCallbackProcessAndDtmCrtNewTrx(Short callbackProcess, Date date) {
		
		Object[][] queryParams = {{TrDocumentH.CALLBACK_PROCESS_HBM, callbackProcess}, {"date", date}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "join fetch docH.amMsuserByIdMsuserCustomer am "
				+ "join fetch docH.msOffice mo "
				+ "join fetch docH.msTenant mt "
				+ "left join fetch docH.msBusinessLine bl "
				+ "where docH.callbackProcess = :callbackProcess and mo.isActive ='1' and am.isActive ='1' "
				+ "and docH.dtmCrt >= :date ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentH> getListDocumentHeaderWithoutUserByCallbackProcessNewTrx(Short callbackProcess) {
		Object[][] queryParams = {{TrDocumentH.CALLBACK_PROCESS_HBM, callbackProcess}};
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH docH "
				+ "join fetch docH.msOffice mo "
				+ "where docH.callbackProcess = :callbackProcess and mo.isActive ='1' ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	// TODO: Should be removed, would cause error even in normal cases
	public TrDocumentD getDocumentDetailByDocumentHeaderIdNewTran(long documentHeaderId) {
		return this.managerDAO.selectOne(
				"from TrDocumentD d "
				+ " join fetch d.trDocumentH dh "
				+ " join fetch d.msPeruriDocType pdt "
				+ " join fetch d.msTenant t "
				+ " where dh.idDocumentH = :idDocumentH ", 
				new Object[][] {{TrDocumentH.ID_DOCUMENT_H_HBM, documentHeaderId}});
	}
	
	@Override
	public List<DocHBean> getListDocumentBasedByCallbackValue(Integer max){
		Map<String, Object> params = new HashMap<>();
		params.put("max", max);

        StringBuilder query = new StringBuilder();
        	query.append(" SELECT callback_process AS \"callbackProcess\", ")
        	.append(" ref_number AS \"refNumber\", ")
        	.append(" tenant_code AS \"tenantCode\" ")
        	.append(" FROM tr_document_h dh ")
		    .append(" join ms_tenant mt on mt.id_ms_tenant = dh.id_ms_tenant ")
        	.append(" WHERE dh.callback_process = 901 OR dh.callback_process = 902 OR dh.callback_process = 903 ")
        	.append(" LIMIT :max ");
        	
        	return this.managerDAO.selectForListString(DocHBean.class, query.toString(), params, null);

	}
	
	@Override
    public List<DeleteOnPremResultBean> getDocToDeleteOnPremResult(Date maxDate, String limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("maxDate", maxDate);
        StringBuilder query = new StringBuilder()
                                .append("select ref_number  as \"refNum\", tenant_code as \"tenantCode\" from ( ")
                                .append("   select distinct ref_number, tenant_code, max(date(stamping_date)) as \"stamping_date\" ")
                                .append("   from tr_document_d_stampduty sdt ")
                                .append("   join tr_document_d d on d.id_document_d = sdt.id_document_d ")
                                .append("   join tr_document_h h on h.id_document_h = d.id_document_h ")
                                .append("   join ms_tenant t on t.id_ms_tenant = h.id_ms_tenant ")
                                .append("   where callback_process = 904 ")
                                .append("   group by ref_number, document_id, tenant_code ")
                                .append(") as a where date(stamping_date) <= :maxDate ")
                                .append("order by stamping_date asc, ref_number asc ")
                                .append("limit " + limit);
        
        return managerDAO.selectForListString(DeleteOnPremResultBean.class, query.toString(), params, null);
    }
	
	@Override
    public int countDocToDeleteOnPremResult(Date maxDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("maxDate", maxDate);
        StringBuilder query = new StringBuilder()
        						.append("select count(*) from ( ")
                                .append("select * from ( ")
                                .append("   select distinct ref_number, max(date(stamping_date)) as \"stamping_date\" ")
                                .append("   from tr_document_d_stampduty sdt ")
                                .append("   join tr_document_d d on d.id_document_d = sdt.id_document_d ")
                                .append("   join tr_document_h h on h.id_document_h = d.id_document_h ")
                                .append("   where callback_process = 904 ")
                                .append("   group by ref_number, document_id ")
                                .append(") as a where date(stamping_date) <= :maxDate ")
                                .append("group by stamping_date, ref_number ")
                                .append(") as b");

        return ((BigInteger) managerDAO.selectOneNativeString(query.toString(), params)).intValue();
    }

	@Override
	public List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigning(String[] documentId) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		
		query
			.append("select document_id as \"documentId\" , coalesce(signing_process, '0') as \"signingProcess\" ")
			.append("from tr_document_d ")
			.append("where document_id in (:docId) ");
		
		return this.managerDAO.selectForListString(CheckDocumentBeforeSigningBean.class, query.toString(), params, null);
	}

	@Override
	public BigInteger getSignNeeded(String[] documentId, AmMsuser user) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		query.append("SELECT COUNT(*) as \"total\" FROM tr_document_d_sign a JOIN tr_document_d b ON a.id_document_d = b.id_document_d \r\n" + 
				"WHERE b.document_id IN (:docId) AND sign_date ISNULL AND a.id_ms_user = :idMsUser ");
		
		return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
	}
	
	@Override
	public BigInteger getDocNeeded(String[] documentId, AmMsuser user) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		query.append("SELECT COUNT(*) as \"total\" FROM tr_document_d_sign a JOIN tr_document_d b ON a.id_document_d = b.id_document_d \r\n" + 
				"WHERE b.document_id IN (:docId) AND total_signed = '0' AND a.id_ms_user = :idMsUser ");
		
		return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<Map<String, Object>> getDocDetailNeedSignVida(long idDocumentH, long idMsUser) {
		StringBuilder query = new StringBuilder();
		
		Object[][] params = new Object[][] {
			{TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH},
			{AmMsuser.ID_MS_USER_HBM, idMsUser}
		};
		
		query
			.append("select distinct on (document_id) document_id, ")
			.append("case when dt.doc_template_name is null then dd.document_name ")
			.append("else dt.doc_template_name end, ")
			.append("dh.ref_number, mv.vendor_code ")
			.append("from tr_document_h dh ")
			.append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			.append("left join ms_doc_template dt on dt.id_doc_template = dd.id_ms_doc_template ")
			.append("join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("join ms_vendor mv on dd.id_ms_vendor = mv.id_ms_vendor ")
			.append("where dh.id_document_h = :idDocumentH ")
			.append("and dds.id_ms_user = :idMsUser and dds.sign_date is null");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	public List<Map<String, Object>> getDocDetailNeedSignVidaWithSamePrioritySequenceDocD(long idDocumentH, long idMsUser,long idDocumentD) {
		StringBuilder query = new StringBuilder();
		
		Object[][] params = new Object[][] {
			{TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH},
			{AmMsuser.ID_MS_USER_HBM, idMsUser},
			{TrDocumentD.ID_DOCUMENT_D_HBM,idDocumentD}
		};
		
		query
			.append("select distinct on (dd.document_id) dd.document_id, ")
			.append("case when dt.doc_template_name is null then dd.document_name ")
			.append("else dt.doc_template_name end, ")
			.append("dh.ref_number, mv.vendor_code ")
			.append("from tr_document_h dh ")
			.append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			.append("left join ms_doc_template dt on dt.id_doc_template = dd.id_ms_doc_template ")
			.append("join tr_document_d_sign dds on dds.id_document_d = dd.id_document_d ")
			.append("join ms_vendor mv on dd.id_ms_vendor = mv.id_ms_vendor ")
			.append("join tr_document_d ddps on ddps.id_document_d = :idDocumentD ")
			.append("where dh.id_document_h = :idDocumentH ")
			.append("and case when dd.priority_sequence is null then 32767 ")
			.append(" when dd.priority_sequence = 0 then 32767 ")
			.append(" else dd.priority_sequence ")
			.append(" End = case when ddps.priority_sequence is null then 32767 ")
			.append(" when ddps.priority_sequence = 0 then 32767 ")
			.append(" else ddps.priority_sequence End " )
			.append("and dds.id_ms_user = :idMsUser and dds.sign_date is null");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	@Override
	public List<Map<String, Object>> getDocIdByDocH(String refNumber, String tenantCode) {
		StringBuilder query = new StringBuilder();
		
		Object[][] params = new Object[][] {
			{TrDocumentH.REF_NUMBER_HBM, refNumber},
			{MsTenant.TENANT_CODE_HBM, tenantCode}
		};
		
		query
			.append("SELECT d.document_id , d.sdt_process, d.id_document_d FROM tr_document_d d JOIN tr_document_h h ")
			.append("ON d.id_document_h = h.id_document_h ")
			.append("JOIN ms_tenant mt ON h.id_ms_tenant = mt.id_ms_tenant ")
			.append("WHERE h.ref_number = :refNumber AND mt.tenant_code = :tenantCode ")
			.append("ORDER BY d.id_document_d ");
		return this.managerDAO.selectAllNativeString(query.toString(), params);
	}
	
	@Override
	public List<TrDocumentDSign> getListDocumentDSignByIdDocumentD(long[] listIdDocumentD) {
		StringBuilder query = new StringBuilder();
		query.append("SELECT id_document_d_sign ")
			 	.append("FROM tr_document_d_sign ")
			 	.append("WHERE id_document_d in (:idDocumentD)");
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, listIdDocumentD);
		
		List<Map<String, Object>> result = this.managerDAO.selectAllNativeString(query.toString(), params);
		List<TrDocumentDSign> lDocDSign = new ArrayList<>();
		
		for(Map<String, Object> id : result) {
			BigInteger idDocD = (BigInteger) id.get("d0");
			TrDocumentDSign docDSign = this.managerDAO.selectOne(TrDocumentDSign.class, idDocD.longValue());
			lDocDSign.add(docDSign);
		}
		
		return lDocDSign;
	}

	@Override
	public List<SignerInfoBean> getListSignerSignStatusByIdDocumentD(long idDocumentD, long idMsVendor) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		params.put("idMsVendor", idMsVendor);
		
		query
			.append("select distinct(ms_vendor_registered_user.signer_registered_email) as \"email\", " + 
					"max(case ms_lov.description " + 
					"when 'Employee' then '1' " + 
					"when 'Customer' then '2' " + 
					"when 'Spouse' then '3' " + 
					"when 'Guarantor' then '4' " + 
					"end) as \"signerType\", " + 
					"max(case " + 
					"when sign_date is not null then '1' " +
					"when tr_document_signing_request.request_status = '1'  then '3' " +
					"when tr_document_signing_request.request_status = '2' then '2' " +
					"when tr_document_signing_request.request_status = '0' then '3' " +
					"when sign_date is null then '0' " + 
					"end) as \"signStatus\", TO_CHAR(max(sign_date), 'YYYY-MM-DD HH24:MI:SS') as \"signDate\" " + 
					"from tr_document_d_sign " + 
					"join ms_vendor_registered_user on ms_vendor_registered_user.id_ms_user = tr_document_d_sign.id_ms_user " + 
					"left join tr_document_signing_request on tr_document_signing_request.id_document_d = tr_document_d_sign.id_document_d and tr_document_signing_request.id_ms_user = ms_vendor_registered_user.id_ms_user " + 
					"left join ms_lov on tr_document_d_sign.lov_signer_type = ms_lov.id_lov " + 
					"where tr_document_d_sign.id_document_d = :idDocumentD and ms_vendor_registered_user.id_ms_vendor = :idMsVendor group by email ");
		
		return this.managerDAO.selectForListString(SignerInfoBean.class, query.toString(), params, null);
	}

	@Override
	public List<Map<String, Object>> getDocumentSignDetails(String documentId, AmMsuser user) {
        Map<String, Object> paramsQuery = new HashMap<>();
		
        paramsQuery.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
        paramsQuery.put("documentId", StringUtils.upperCase(documentId));
        
		StringBuilder query = new StringBuilder();
		query		
			.append(" with ddsCTE as ")
			.append(" ( ")
			.append("    SELECT dds.id_document_d_sign, dds.id_ms_user, dd.id_document_d , dds.sign_date, ")
			.append("           dt.doc_template_name, lovdocType.description AS doc_type_name , usercust.full_name, ")
			.append("           dh.ref_number, lovDocType.description,  case when dd.id_ms_doc_template is not null then dt.doc_template_name else dd.document_name end AS doc_name, ")
			.append("           usercust.full_name as customer_name, dd.request_date, dd.completed_date, dd.document_id, ")
			.append("           office.office_name, region.region_name,  lovSignStat.description  AS sign_stat, ")
			.append("           dd.total_materai, dd.total_stamping, dh.automatic_stamping_after_sign AS \"status_otomatis_stamping\", ")
			.append("           dh.proses_materai AS \"status_proses_materai\" , ms_vendor.vendor_code AS \"vendor_code\", dd.signing_process AS \"signing_process\" ")
			.append("    FROM   tr_document_d dd ")
			.append("    JOIN LATERAL ( ")
					.append(" select dds.* from tr_document_d_sign dds ")
					.append(" where dds.id_document_d = dd.id_document_d ");
					query.append(" and dds.id_ms_user = :idMsUser ");
					query.append(" limit 1 ")
			.append("    ) dds on true ")
			.append("    JOIN   ms_vendor  ON dd.id_ms_vendor = ms_vendor.id_ms_vendor")
			.append("    JOIN   tr_document_h dh   ON   dh.id_document_h = dd.id_document_h AND dh.is_active = '1' ")
			.append("    JOIN   ms_tenant mt       ON   dh.id_ms_tenant = mt.id_ms_tenant ")
			.append("    LEFT JOIN   am_msuser usercust ON   dh.id_msuser_customer = usercust.id_ms_user ")
			.append("    LEFT JOIN   ms_doc_template dt ON   dd.id_ms_doc_template = dt.id_doc_template ")
			.append("    JOIN   ms_lov lovDocType  ON   dh.lov_doc_type = lovDocType.id_lov ")
			.append("    JOIN   ms_lov lovSignStat ON   dd.lov_sign_status = lovSignStat.id_lov ")
			.append("    JOIN   ms_office office   ON   office.id_ms_office = dh.id_ms_office ")
			.append("    LEFT JOIN ms_region region ON  region.id_ms_region = office.id_ms_region ")
			.append(" WHERE 1 = 1 ")
			.append(" and dd.document_id = :documentId ")
			.append(" ) ")
				.append(" SELECT * FROM ( ")
					.append(" SELECT DISTINCT ON (id_document_d) ROW_NUMBER() OVER(ORDER BY request_date ASC) AS row, ")
					.append(" ref_number, doc_type_name, doc_name, customer_name, ")
					.append(" TO_CHAR(request_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS request_date, ")
					.append(" TO_CHAR(completed_date, '"+GlobalVal.POSTGRE_DATE_TIME_FORMAT_SEC +"') AS completed_date, document_id, ")
					.append(" CONCAT(COALESCE(countSigned,'0'), ' / ', COALESCE(countSigner,'0')) AS total_signed, ")
					.append(" ddsCTE.id_document_d,  office_name, region_name, sign_stat, ")
					.append(" concat(COALESCE(total_stamping, '0'), '/', COALESCE(total_materai, '0')), ")
					.append(" status_otomatis_stamping, ")
					.append(" status_proses_materai, vendor_code , signing_process ")
					.append(" FROM ddsCTE ")
					.append(" JOIN LATERAL ( ")
						.append(" SELECT dds.id_document_d, COUNT (DISTINCT dds.id_ms_user) AS countSigner, ")
							.append(" COUNT (DISTINCT dds.id_ms_user) FILTER (WHERE dds.sign_date IS NOT NULL) AS countSigned ")
						.append(" FROM tr_document_d_sign dds ")
						.append(" WHERE dds.id_document_d = ddsCTE.id_document_d ")
						.append(" GROUP BY dds.id_document_d ")
					.append(" ) countSign ON TRUE ")
				.append(" ) as a ");
			
		
		return this.managerDAO.selectAllNativeString(query.toString(), paramsQuery);
	}

	@Override
	public BigInteger getDistinctSingerByIdDocumentDAndIdMsUser(Long[] listIdDocumentD, long idMsUser) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put("listIdDocumentD", listIdDocumentD);
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		query
		.append("select distinct(id_ms_user) from tr_document_d_sign where id_document_d in (:listIdDocumentD) \r\n" + 
				"and id_ms_user = :idMsUser");
		
		return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
	}

	
	@Override
	public int countTrManualReport(GetListReportRequest request) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put("tenant", request.getTenantCode());
		
		query
		.append("select count (*) " + 
				"from tr_manual_report mr JOIN ms_Tenant mt "
				+ "ON (mr.id_ms_tenant = mt.id_ms_tenant) WHERE mt.tenant_code =:tenant ");
		
		List<Map<String, Object>> total = this.getManagerDAO().selectAllNativeString(query.toString(), params);
		
		String totalString = total.get(0).toString();
		int start = total.get(0).toString().indexOf("=");
		int end = total.get(0).toString().indexOf("}");
		String subString = totalString.substring(start + 1, end);

		return Integer.parseInt(subString);
	}

	@Override
	public List<ListReportBean> getReportyByIdTenantPage(String tenantCode, int min, int max) {
		Map<String, Object> params = new HashMap<>();
		params.put("min", min);
		params.put("max", max);
		params.put("tenant",tenantCode);
		
		StringBuilder query = new StringBuilder();
		
		query.append(" with report AS " + 
				"( " + 
				"SELECT tr.id_manual_report AS \"idManualReport\" , CONCAT(to_char(tr.report_date, 'Month'), to_char(tr.report_date, 'YYYY')) AS \"periode\", " + 
				"ml.description AS \"reportType\", tr.filename AS \"filename\", " + 
				"row_number() over (order by tr.report_date DESC) AS \"rowNum\" " + 
				"FROM tr_manual_report tr " + 
				"JOIN ms_lov ml ON ml.id_lov = tr.lov_report_type " + 
				"JOIN ms_tenant mt ON mt.id_ms_tenant = tr.id_ms_tenant " + 
				"WHERE mt.tenant_code = :tenant " + 
				"order by tr.report_date DESC " + 
				") " + 
				"SELECT \"idManualReport\" , \"periode\", \"reportType\", \"filename\" " + 
				"FROM report " + 
				"WHERE \"rowNum\" between :min and :max ");
				
		
		return this.managerDAO.selectForListString(ListReportBean.class, query.toString(), params, null);
	}

	@Override
	public TrManualReport getDocReportById(long idManualReport) {
		Object[][] params = {{TrManualReport.ID_MANUAL_REPORT_HBM, idManualReport}
			};
			
			return this.managerDAO.selectOne(
					"from TrManualReport tr "
					+ "WHERE tr.idManualReport = :idManualReport ",
					params);
	}

	@Override
	public List<TrDocumentSigningRequest> getListTrDocumentSigningRequestByIdMsUserAndIdDocumentH(long idMsUser,
			long idDocumentH) {
		Object[][] queryParams = {
				{AmMsuser.ID_MS_USER_HBM, idMsUser},
				{"idDocumentH", idDocumentH}};
		
		return (List<TrDocumentSigningRequest>) this.managerDAO.list(
				"from TrDocumentSigningRequest tdSignReq "
				+ "join fetch tdSignReq.amMsuser am "
				+ "join fetch tdSignReq.trDocumentH th "
				+ "where am.idMsUser = :idMsUser and th.idDocumentH = :idDocumentH ", queryParams)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public BigInteger getTotalUnsignedDoc(long idMsTenant, long idMsUser) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put("idMsTenant", idMsTenant);
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		query
			.append("SELECT COUNT (DISTINCT tds.id_document_d) ")
			.append("FROM tr_document_d_sign tds ")
			.append("JOIN tr_document_d td ON tds.id_document_d = td.id_document_d ")
			.append("JOIN tr_document_h th ON td.id_document_h = th.id_document_h ")
			.append("WHERE id_ms_user = :idMsUser ")
			.append("AND sign_date IS NULL ")
			.append("AND td.id_ms_tenant = :idMsTenant ")
			.append("AND th.is_active = '1' ");
		
		return (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
	}

	@Override
	public List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigningEmbed(String[] documentId,
			long idMsUser) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		query
			.append("select document_id as \"documentId\", " + 
					"CASE WHEN request_status = 1 THEN '1' " + 
					"WHEN request_status = 2 THEN '2' " + 
					"WHEN request_status = 3 THEN '3' " + 
					"END AS \"signingProcess\" " + 
					"FROM tr_document_signing_request tds JOIN tr_document_d td ON (tds.id_document_d = td.id_document_d) " + 
					"WHERE tds.id_ms_user = :idMsUser AND document_id IN (:docId) ");
		
	
		return this.managerDAO.selectForListString(CheckDocumentBeforeSigningBean.class, query.toString(), params, null);
	

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public List<TrDocumentH> getListDocumentHeaderByCallbackProcessAndIsManualUploadNewTrx(Short callbackProcess, String isManualUpload) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.CALLBACK_PROCESS_HBM, callbackProcess);
		params.put("isManualUpload", isManualUpload);
		
		
		return (List<TrDocumentH>) this.managerDAO.list(
				"from TrDocumentH dh "
				+ "join fetch dh.msTenant mt "
				+ "left join fetch mt.lovVendorStamping lvs "
				+ "where dh.callbackProcess = :callbackProcess "
				+ "and dh.isManualUpload = :isManualUpload ", params)
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public int countStampDutyNeeded(TrDocumentH documentH) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, documentH.getIdDocumentH());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select COALESCE(sum(total_materai), 0) from tr_document_d ")
			.append("where id_document_h = :idDocumentH ");
		
		BigInteger totalData = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == totalData) {
			return 0;
		}
		
		return totalData.intValue();
	}

	@Override
	public TrDocumentDSign getUnsignedDocumentDSign(TrDocumentD document) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, document.getIdDocumentD());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_document_d_sign ")
			.append("from tr_document_d_sign ")
			.append("where id_document_d = :idDocumentD ")
			.append("and sign_date is null ")
			.append("order by seq_no asc limit 1 ");
		
		BigInteger idDocumentDSign = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentDSign) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrDocumentDSign dds "
				+ "join fetch dds.amMsuser mu "
				+ "join fetch dds.trDocumentD dd "
				+ "where dds.idDocumentDSign = :idDocumentDSign ", new Object[][] {{"idDocumentDSign", idDocumentDSign.longValue()}});
	}

	@Override
	public List<MsDocTemplateSignLoc> getListSignLocationByTemplateCodeTenantCodeAndLovSignerType(
			String documentTemplateCode, String signerTypeCode, String tenantCode) {
		Object[][] queryParams = {
				{MsDocTemplate.DOCUMENT_TEMPLATE_CODE_HBM, StringUtils.upperCase(documentTemplateCode)},
				{MsLov.LOV_GROUP_HBM, GlobalVal.LOV_GROUP_SIGNER_TYPE},
				{MsLov.CODE_HBM, StringUtils.upperCase(signerTypeCode)},
				{MsTenant.TENANT_CODE_HBM, StringUtils.upperCase(tenantCode)}
		};
		
		return (List<MsDocTemplateSignLoc>) this.managerDAO.list(
				" from MsDocTemplateSignLoc dtsl " 
				+ " join fetch dtsl.msDocTemplate dt "
				+ " join fetch dt.msTenant mt "
				+ " left join fetch dtsl.msLovByLovSignerType mlSigner  "
				+ " join fetch dtsl.msLovByLovSignType mlSign "
				+ " where dt.docTemplateCode = :docTemplateCode "
				+ " and mlSigner.lovGroup = :lovGroup and mlSigner.code = :code "
				+ " and mt.tenantCode = :tenantCode "
				+ " order by dtsl.signPage, dtsl.seqNo ",queryParams 
				).get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<CheckDocumentBeforeSigningBean> getListCheckDocumentBeforeSigningFromTrDocumentH(String[] documentId) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		
		query
			.append("select document_id as \"documentId\" , coalesce(tr_document_h.signing_process, '0') as \"signingProcess\" ")
			.append("from tr_document_d join tr_document_h on tr_document_d.id_document_h = tr_document_h.id_document_h ")
			.append("where document_id in (:docId) ");
		
		return this.managerDAO.selectForListString(CheckDocumentBeforeSigningBean.class, query.toString(), params, null);
	}

	@Override
	public List<CheckDocumentBeforeSigningBean> getListDocIdAndSigningProcessPrivy(String[] documentId, long idMsUser) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.DOC_ID_HBM, documentId);
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		query
			.append("select document_id as \"documentId\", CAST((coalesce(request_status, '0')) AS varchar) as \"signingProcess\" " + 
					"from tr_document_d left outer join tr_document_signing_request_detail " + 
					"on tr_document_signing_request_detail.id_document_d = tr_document_d.id_document_d " + 
					"left outer join  tr_document_signing_request on tr_document_signing_request.id_document_signing_request = tr_document_signing_request_detail.id_document_signing_request " + 
					"where tr_document_signing_request.id_ms_user = :idMsUser and document_id in (:docId) ");
		
	
		return this.managerDAO.selectForListString(CheckDocumentBeforeSigningBean.class, query.toString(), params, null);
	}

	@Override
	public List<SignerInfoBean> getListSignerSignStatusPrivyByIdDocumentD(long idDocumentD, long idMsVendor) {
		StringBuilder query = new StringBuilder();
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD);
		params.put("idMsVendor", idMsVendor);
		
		query
			.append("select distinct(ms_vendor_registered_user.signer_registered_email) as \"email\", " + 
					"max(case ms_lov.description " + 
					"when 'Employee' then '1' " + 
					"when 'Customer' then '2' " + 
					"when 'Spouse' then '3' " + 
					"when 'Guarantor' then '4' " + 
					"end) as \"signerType\", " + 
					"max(case " + 
					"when sign_date is not null then '1' " +
					"when tr_document_signing_request.request_status = '1'  then '3' " +
					"when tr_document_signing_request.request_status = '2' then '2' " +
					"when tr_document_signing_request.request_status = '0' then '3' " +
					"when sign_date is null then '0' " + 
					"end) as \"signStatus\", TO_CHAR(max(sign_date), 'YYYY-MM-DD HH24:MI:SS') as \"signDate\" " + 
					"from tr_document_d_sign " + 
					"join ms_vendor_registered_user on ms_vendor_registered_user.id_ms_user = tr_document_d_sign.id_ms_user " + 
					"left join tr_document_signing_request_detail on tr_document_signing_request_detail.id_document_d = tr_document_d_sign.id_document_d " + 
					"left join tr_document_signing_request on tr_document_signing_request.id_document_signing_request = tr_document_signing_request_detail.id_document_signing_request and tr_document_signing_request.id_ms_user = ms_vendor_registered_user.id_ms_user " + 
					"left join ms_lov on tr_document_d_sign.lov_signer_type = ms_lov.id_lov " + 
					"where tr_document_d_sign.id_document_d = :idDocumentD and ms_vendor_registered_user.id_ms_vendor = :idMsVendor group by email ");
		
		return this.managerDAO.selectForListString(SignerInfoBean.class, query.toString(), params, null);
	}

	@Override
	public void insertPsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation) {
		psreSigningConfirmation.setUsrCrt(MssTool.maskData(psreSigningConfirmation.getUsrCrt()));
		this.managerDAO.insert(psreSigningConfirmation);
	}

	@Override
	public void updatePsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation) {
		psreSigningConfirmation.setUsrUpd(MssTool.maskData(psreSigningConfirmation.getUsrUpd()));
		this.managerDAO.update(psreSigningConfirmation);
	}

	@Override
	public void deletePsreSigningConfirmation(TrPsreSigningConfirmation psreSigningConfirmation) {
		this.managerDAO.delete(psreSigningConfirmation);
	}

	@Override
	public TrPsreSigningConfirmation getTrPsreSigningConfirmationByIdMsUser(long idMsUser) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		query
		.append("SELECT id_psre_signing_confirmation FROM tr_psre_signing_confirmation " + 
				"WHERE id_ms_user = :idMsUser " +
				"ORDER BY dtm_crt DESC LIMIT 1 ");
		
		BigInteger idPsreSigningConfirmation = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idPsreSigningConfirmation) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrPsreSigningConfirmation tpsc "
				+ "join fetch tpsc.amMsuser amu "
				+ "where tpsc.idPsreSigningConfirmation = :idPsreSigningConfirmation ",
				new Object[][] {{"idPsreSigningConfirmation", idPsreSigningConfirmation.longValue()}});
	}

	@Override
	public TrPsreSigningConfirmation getTrPsreSigningConfirmationByIdMsUserAndDocumentId(long idMsUser,
			String documentId) {
		Map<String, Object> params = new HashMap<>();
		
		StringBuilder query = new StringBuilder();
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		params.put("documentId",documentId);
		
		query
		.append("SELECT id_psre_signing_confirmation FROM tr_psre_signing_confirmation " + 
				"WHERE id_ms_user = :idMsUser  AND document_ids = :documentId " +
				"ORDER BY dtm_crt DESC LIMIT 1 ");
		
		BigInteger idPsreSigningConfirmation = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idPsreSigningConfirmation) {
			return null;
		}
		
		return this.managerDAO.selectOne(
				"from TrPsreSigningConfirmation tpsc "
				+ "join fetch tpsc.amMsuser amu "
				+ "where tpsc.idPsreSigningConfirmation = :idPsreSigningConfirmation ",
				new Object[][] {{"idPsreSigningConfirmation", idPsreSigningConfirmation.longValue()}});
	
	}

	@Override
	public TrDocumentH getLatestUnsignedAgreement(AmMsuser user, MsTenant tenant, MsVendor vendor) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		params.put(MsTenant.ID_TENANT_HBM, tenant.getIdMsTenant());
		params.put(MsVendor.ID_VENDOR_HBM, vendor.getIdMsVendor());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dd.id_document_h ")
			.append("from tr_document_d_sign dds ")
			.append("join tr_document_d dd on dds.id_document_d = dd.id_document_d ")
			.append("where dds.id_ms_user = :idMsUser ")
			.append("and dd.id_ms_tenant = :idMsTenant ")
			.append("and dd.id_ms_vendor = :idMsVendor ")
			.append("order by id_document_d_sign desc limit 1 ");
		
		BigInteger idDocumentH = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentH) {
			return null;
		}
		
		Map<String, Object> queryParams = new HashMap<>();
		queryParams.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH.longValue());
		
		return managerDAO.selectOne(
				"from TrDocumentH dh "
				+ "join fetch dh.msTenant mt "
				+ "where dh.idDocumentH = :idDocumentH ", queryParams);
	}

	@Override
	public TrDocumentD getLatestDocumentDetailBySigner(AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_document_d ")
			.append("from tr_document_d_sign ")
			.append("where id_ms_user = :idMsUser ")
			.append("order by id_document_d_sign desc limit 1 ");
		
		BigInteger idDocumentD = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentD) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrDocumentD dd "
				+ "join fetch dd.msTenant "
				+ "join fetch dd.trDocumentH dh "
				+ "left join fetch dh.msOffice mo "
				+ "left join fetch dh.msBusinessLine mbl "
				+ "where dd.idDocumentD = :idDocumentD ", new Object[][] {{ TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD.longValue() }});
	}

	@Override
	public TrDocumentD getLatestDocumentDetailBySentDateSigner(AmMsuser user) {
		Map<String, Object> params = new HashMap<>();
		params.put(AmMsuser.ID_MS_USER_HBM, user.getIdMsUser());
		
		StringBuilder query = new StringBuilder();
		query
			.append("select id_document_d ")
			.append("from tr_document_d_sign ")
			.append("where id_ms_user = :idMsUser ")
			.append("order by sent_date desc limit 1 ");
		
		BigInteger idDocumentD = (BigInteger) managerDAO.selectOneNativeString(query.toString(), params);
		if (null == idDocumentD) {
			return null;
		}
		
		return managerDAO.selectOne(
				"from TrDocumentD dd "
				+ "join fetch dd.msTenant "
				+ "join fetch dd.trDocumentH dh "
				+ "left join fetch dh.msOffice mo "
				+ "left join fetch dh.msBusinessLine mbl "
				+ "where dd.idDocumentD = :idDocumentD ", new Object[][] {{ TrDocumentD.ID_DOCUMENT_D_HBM, idDocumentD.longValue() }});
	}

	@Override
	public Short getHighestPriorityUnsignedDocument(Long idDocumentH, Long idMsUser) {
		Map<String, Object> params = new HashMap<>();
		params.put(TrDocumentH.ID_DOCUMENT_H_HBM, idDocumentH);
		params.put(AmMsuser.ID_MS_USER_HBM, idMsUser);
		
		StringBuilder query = new StringBuilder();
		query
			.append("select dd.priority_sequence ")
			.append("from tr_document_h dh ")
			.append("join tr_document_d dd on dh.id_document_h = dd.id_document_h ")
			.append("join tr_document_d_sign dds on dd.id_document_d = dds.id_document_d ")
			.append("where dh.id_document_h = :idDocumentH ")
			.append("and dd.priority_sequence > 0 ")
			.append("and dds.id_ms_user = :idMsUser ")
			.append("and dds.sign_date is null ")
			.append("order by dd.priority_sequence asc limit 1 ");
		
		Short priority = (Short) managerDAO.selectOneNativeString(query.toString(), params);
		return null != priority ? priority : 0;
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void updateCallbackProcessNewTranNative(long idDocumentH, String usrUpd, short callbackProcess) {
		Map<String, Object> params = new HashMap<>();
		params.put("usrUpd", MssTool.maskData(usrUpd));
		params.put("idDocumentH", idDocumentH);
		params.put("callbackProcess", callbackProcess);

		StringBuilder query = new StringBuilder();
		query.append(" UPDATE tr_document_h SET dtm_upd = NOW(), usr_upd = :usrUpd, callback_process = :callbackProcess WHERE id_document_h = :idDocumentH ");
		
		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	public void updateCallbackProcessAndRetryResumeAttemptNumNewTranNative(Long idDocumentH, String usrUpd,
			Short callbackProcess, Short retryResumeAttemptNumNewTranNative) {
		Map<String, Object> params = new HashMap<>();
		params.put("usrUpd", MssTool.maskData(usrUpd));
		params.put("idDocumentH", idDocumentH);
		params.put("callbackProcess", callbackProcess);
		params.put("retryResumeAttemptNum", retryResumeAttemptNumNewTranNative);

		StringBuilder query = new StringBuilder();
		query.append(" UPDATE tr_document_h SET dtm_upd = NOW(), usr_upd = :usrUpd, callback_process = :callbackProcess, retry_resume_attempt_num = :retryResumeAttemptNum WHERE id_document_h = :idDocumentH");
		
		managerDAO.updateNativeString(query.toString(), params);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void insertDocumentRestore(TrDocumentDRestore documentRestore) {
		documentRestore.setUsrCrt(MssTool.maskData(documentRestore.getUsrCrt()));
		this.managerDAO.insert(documentRestore);
	}
}
