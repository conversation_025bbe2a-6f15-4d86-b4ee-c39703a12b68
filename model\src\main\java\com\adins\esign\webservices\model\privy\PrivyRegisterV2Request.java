package com.adins.esign.webservices.model.privy;

import java.io.Serializable;

public class PrivyRegisterV2Request implements Serializable {

    private static final long serialVersionUID = -8333076410L;    
    
    private String trId;
    private String nik;
    private String name;
    private String dob;
    private String email;
    private String phone;
    private String selfie;
    private String ktp;

    public String getTrId() {
        return this.trId;
    }

    public void setTrId(String trId) {
        this.trId = trId;
    }

    public String getNik() {
        return this.nik;
    }

    public void setNik(String nik) {
        this.nik = nik;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDob() {
        return this.dob;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSelfie() {
        return this.selfie;
    }

    public void setSelfie(String selfie) {
        this.selfie = selfie;
    }

    public String getKtp() {
        return this.ktp;
    }

    public void setKtp(String ktp) {
        this.ktp = ktp;
    }

}
